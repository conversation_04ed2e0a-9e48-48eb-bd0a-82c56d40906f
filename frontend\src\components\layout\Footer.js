import React from 'react';
import styled from 'styled-components';

const FooterContainer = styled.footer`
  background-color: ${({ theme }) => theme.colors.surface};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  padding: ${({ theme }) => theme.spacing.lg} 0;
  margin-top: auto;
`;

const FooterContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
  text-align: center;
`;

const FooterText = styled.p`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSize.sm};
  margin: 0;
`;

const FooterLinks = styled.div`
  display: flex;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
    gap: ${({ theme }) => theme.spacing.sm};
  }
`;

const FooterLink = styled.a`
  color: ${({ theme }) => theme.colors.textSecondary};
  text-decoration: none;
  font-size: ${({ theme }) => theme.fontSize.sm};
  transition: color ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    color: ${({ theme }) => theme.colors.primary};
  }
`;

const Footer = () => {
  return (
    <FooterContainer>
      <FooterContent>
        <FooterLinks>
          <FooterLink href="#" onClick={(e) => e.preventDefault()}>
            About
          </FooterLink>
          <FooterLink href="#" onClick={(e) => e.preventDefault()}>
            Privacy Policy
          </FooterLink>
          <FooterLink href="#" onClick={(e) => e.preventDefault()}>
            Terms of Service
          </FooterLink>
          <FooterLink href="#" onClick={(e) => e.preventDefault()}>
            Contact
          </FooterLink>
        </FooterLinks>
        <FooterText>
          © 2025 WeatherApp. All rights reserved. Powered by OpenWeatherMap API.
        </FooterText>
      </FooterContent>
    </FooterContainer>
  );
};

export default Footer;
