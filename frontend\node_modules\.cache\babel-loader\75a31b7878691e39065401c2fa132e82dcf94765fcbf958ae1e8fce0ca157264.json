{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI, setAuthToken, getStoredToken, getStoredUser, storeUser, clearStorage } from '../services/api';\n\n// Initial state\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  SET_LOADING: 'SET_LOADING',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGOUT: 'LOGOUT',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER'\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null\n      };\n    case AUTH_ACTIONS.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: action.payload\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = /*#__PURE__*/createContext();\n\n// Provider component\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state on app load\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = getStoredToken();\n      const user = getStoredUser();\n      if (token && user) {\n        setAuthToken(token);\n        try {\n          // Verify token is still valid\n          const response = await authAPI.getUser();\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: response.data.user,\n              token\n            }\n          });\n          storeUser(response.data.user);\n        } catch (error) {\n          // Token is invalid\n          clearStorage();\n          dispatch({\n            type: AUTH_ACTIONS.LOGOUT\n          });\n        }\n      } else {\n        dispatch({\n          type: AUTH_ACTIONS.SET_LOADING,\n          payload: false\n        });\n      }\n    };\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async credentials => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.SET_LOADING,\n        payload: true\n      });\n      dispatch({\n        type: AUTH_ACTIONS.CLEAR_ERROR\n      });\n      const response = await authAPI.login(credentials);\n      const {\n        user,\n        token\n      } = response.data;\n      setAuthToken(token);\n      storeUser(user);\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Register function\n  const register = async userData => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.SET_LOADING,\n        payload: true\n      });\n      dispatch({\n        type: AUTH_ACTIONS.CLEAR_ERROR\n      });\n      const response = await authAPI.register(userData);\n      const {\n        user,\n        token\n      } = response.data;\n      setAuthToken(token);\n      storeUser(user);\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: {\n          user,\n          token\n        }\n      });\n      return {\n        success: true,\n        user\n      };\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    clearStorage();\n    setAuthToken(null);\n    dispatch({\n      type: AUTH_ACTIONS.LOGOUT\n    });\n  };\n\n  // Update user profile\n  const updateProfile = async profileData => {\n    try {\n      dispatch({\n        type: AUTH_ACTIONS.CLEAR_ERROR\n      });\n      const response = await authAPI.updateProfile(profileData);\n      const updatedUser = response.data.user;\n      storeUser(updatedUser);\n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_USER,\n        payload: updatedUser\n      });\n      return {\n        success: true,\n        user: updatedUser\n      };\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      const errorMessage = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Profile update failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage\n      });\n      return {\n        success: false,\n        error: errorMessage\n      };\n    }\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({\n      type: AUTH_ACTIONS.CLEAR_ERROR\n    });\n  };\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateProfile,\n    clearError\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 212,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use auth context\n_s(AuthProvider, \"bgCdjuTOmPdSBRwTap80EFd9Y3U=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default AuthContext;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useReducer", "useEffect", "authAPI", "setAuthToken", "getStoredToken", "getStoredUser", "storeUser", "clearStorage", "jsxDEV", "_jsxDEV", "initialState", "user", "token", "isAuthenticated", "isLoading", "error", "AUTH_ACTIONS", "SET_LOADING", "LOGIN_SUCCESS", "LOGOUT", "SET_ERROR", "CLEAR_ERROR", "UPDATE_USER", "authReducer", "state", "action", "type", "payload", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "initializeAuth", "response", "getUser", "data", "login", "credentials", "success", "_error$response", "_error$response$data", "errorMessage", "message", "register", "userData", "_error$response2", "_error$response2$data", "logout", "updateProfile", "profileData", "updatedUser", "_error$response3", "_error$response3$data", "clearError", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useReducer, useEffect } from 'react';\nimport { authAPI, setAuthToken, getStoredToken, getStoredUser, storeUser, clearStorage } from '../services/api';\n\n// Initial state\nconst initialState = {\n  user: null,\n  token: null,\n  isAuthenticated: false,\n  isLoading: true,\n  error: null,\n};\n\n// Action types\nconst AUTH_ACTIONS = {\n  SET_LOADING: 'SET_LOADING',\n  LOGIN_SUCCESS: 'LOGIN_SUCCESS',\n  LOGOUT: 'LOGOUT',\n  SET_ERROR: 'SET_ERROR',\n  CLEAR_ERROR: 'CLEAR_ERROR',\n  UPDATE_USER: 'UPDATE_USER',\n};\n\n// Reducer\nconst authReducer = (state, action) => {\n  switch (action.type) {\n    case AUTH_ACTIONS.SET_LOADING:\n      return {\n        ...state,\n        isLoading: action.payload,\n      };\n    case AUTH_ACTIONS.LOGIN_SUCCESS:\n      return {\n        ...state,\n        user: action.payload.user,\n        token: action.payload.token,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.LOGOUT:\n      return {\n        ...state,\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case AUTH_ACTIONS.SET_ERROR:\n      return {\n        ...state,\n        error: action.payload,\n        isLoading: false,\n      };\n    case AUTH_ACTIONS.CLEAR_ERROR:\n      return {\n        ...state,\n        error: null,\n      };\n    case AUTH_ACTIONS.UPDATE_USER:\n      return {\n        ...state,\n        user: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\n// Create context\nconst AuthContext = createContext();\n\n// Provider component\nexport const AuthProvider = ({ children }) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // Initialize auth state on app load\n  useEffect(() => {\n    const initializeAuth = async () => {\n      const token = getStoredToken();\n      const user = getStoredUser();\n\n      if (token && user) {\n        setAuthToken(token);\n        try {\n          // Verify token is still valid\n          const response = await authAPI.getUser();\n          dispatch({\n            type: AUTH_ACTIONS.LOGIN_SUCCESS,\n            payload: {\n              user: response.data.user,\n              token,\n            },\n          });\n          storeUser(response.data.user);\n        } catch (error) {\n          // Token is invalid\n          clearStorage();\n          dispatch({ type: AUTH_ACTIONS.LOGOUT });\n        }\n      } else {\n        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });\n      }\n    };\n\n    initializeAuth();\n  }, []);\n\n  // Login function\n  const login = async (credentials) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });\n      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n\n      const response = await authAPI.login(credentials);\n      const { user, token } = response.data;\n\n      setAuthToken(token);\n      storeUser(user);\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, token },\n      });\n\n      return { success: true, user };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Login failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Register function\n  const register = async (userData) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });\n      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n\n      const response = await authAPI.register(userData);\n      const { user, token } = response.data;\n\n      setAuthToken(token);\n      storeUser(user);\n\n      dispatch({\n        type: AUTH_ACTIONS.LOGIN_SUCCESS,\n        payload: { user, token },\n      });\n\n      return { success: true, user };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Registration failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    clearStorage();\n    setAuthToken(null);\n    dispatch({ type: AUTH_ACTIONS.LOGOUT });\n  };\n\n  // Update user profile\n  const updateProfile = async (profileData) => {\n    try {\n      dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n\n      const response = await authAPI.updateProfile(profileData);\n      const updatedUser = response.data.user;\n\n      storeUser(updatedUser);\n      dispatch({\n        type: AUTH_ACTIONS.UPDATE_USER,\n        payload: updatedUser,\n      });\n\n      return { success: true, user: updatedUser };\n    } catch (error) {\n      const errorMessage = error.response?.data?.message || 'Profile update failed';\n      dispatch({\n        type: AUTH_ACTIONS.SET_ERROR,\n        payload: errorMessage,\n      });\n      return { success: false, error: errorMessage };\n    }\n  };\n\n  // Clear error\n  const clearError = () => {\n    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });\n  };\n\n  const value = {\n    ...state,\n    login,\n    register,\n    logout,\n    updateProfile,\n    clearError,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,QAAQ,OAAO;AAC/E,SAASC,OAAO,EAAEC,YAAY,EAAEC,cAAc,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,QAAQ,iBAAiB;;AAE/G;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,MAAMC,YAAY,GAAG;EACnBC,WAAW,EAAE,aAAa;EAC1BC,aAAa,EAAE,eAAe;EAC9BC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,YAAY,CAACC,WAAW;MAC3B,OAAO;QACL,GAAGO,KAAK;QACRV,SAAS,EAAEW,MAAM,CAACE;MACpB,CAAC;IACH,KAAKX,YAAY,CAACE,aAAa;MAC7B,OAAO;QACL,GAAGM,KAAK;QACRb,IAAI,EAAEc,MAAM,CAACE,OAAO,CAAChB,IAAI;QACzBC,KAAK,EAAEa,MAAM,CAACE,OAAO,CAACf,KAAK;QAC3BC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACG,MAAM;MACtB,OAAO;QACL,GAAGK,KAAK;QACRb,IAAI,EAAE,IAAI;QACVC,KAAK,EAAE,IAAI;QACXC,eAAe,EAAE,KAAK;QACtBC,SAAS,EAAE,KAAK;QAChBC,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACI,SAAS;MACzB,OAAO;QACL,GAAGI,KAAK;QACRT,KAAK,EAAEU,MAAM,CAACE,OAAO;QACrBb,SAAS,EAAE;MACb,CAAC;IACH,KAAKE,YAAY,CAACK,WAAW;MAC3B,OAAO;QACL,GAAGG,KAAK;QACRT,KAAK,EAAE;MACT,CAAC;IACH,KAAKC,YAAY,CAACM,WAAW;MAC3B,OAAO;QACL,GAAGE,KAAK;QACRb,IAAI,EAAEc,MAAM,CAACE;MACf,CAAC;IACH;MACE,OAAOH,KAAK;EAChB;AACF,CAAC;;AAED;AACA,MAAMI,WAAW,gBAAG9B,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAM+B,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACP,KAAK,EAAEQ,QAAQ,CAAC,GAAGhC,UAAU,CAACuB,WAAW,EAAEb,YAAY,CAAC;;EAE/D;EACAT,SAAS,CAAC,MAAM;IACd,MAAMgC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,MAAMrB,KAAK,GAAGR,cAAc,CAAC,CAAC;MAC9B,MAAMO,IAAI,GAAGN,aAAa,CAAC,CAAC;MAE5B,IAAIO,KAAK,IAAID,IAAI,EAAE;QACjBR,YAAY,CAACS,KAAK,CAAC;QACnB,IAAI;UACF;UACA,MAAMsB,QAAQ,GAAG,MAAMhC,OAAO,CAACiC,OAAO,CAAC,CAAC;UACxCH,QAAQ,CAAC;YACPN,IAAI,EAAEV,YAAY,CAACE,aAAa;YAChCS,OAAO,EAAE;cACPhB,IAAI,EAAEuB,QAAQ,CAACE,IAAI,CAACzB,IAAI;cACxBC;YACF;UACF,CAAC,CAAC;UACFN,SAAS,CAAC4B,QAAQ,CAACE,IAAI,CAACzB,IAAI,CAAC;QAC/B,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd;UACAR,YAAY,CAAC,CAAC;UACdyB,QAAQ,CAAC;YAAEN,IAAI,EAAEV,YAAY,CAACG;UAAO,CAAC,CAAC;QACzC;MACF,CAAC,MAAM;QACLa,QAAQ,CAAC;UAAEN,IAAI,EAAEV,YAAY,CAACC,WAAW;UAAEU,OAAO,EAAE;QAAM,CAAC,CAAC;MAC9D;IACF,CAAC;IAEDM,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,KAAK,GAAG,MAAOC,WAAW,IAAK;IACnC,IAAI;MACFN,QAAQ,CAAC;QAAEN,IAAI,EAAEV,YAAY,CAACC,WAAW;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;MAC3DK,QAAQ,CAAC;QAAEN,IAAI,EAAEV,YAAY,CAACK;MAAY,CAAC,CAAC;MAE5C,MAAMa,QAAQ,GAAG,MAAMhC,OAAO,CAACmC,KAAK,CAACC,WAAW,CAAC;MACjD,MAAM;QAAE3B,IAAI;QAAEC;MAAM,CAAC,GAAGsB,QAAQ,CAACE,IAAI;MAErCjC,YAAY,CAACS,KAAK,CAAC;MACnBN,SAAS,CAACK,IAAI,CAAC;MAEfqB,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACE,aAAa;QAChCS,OAAO,EAAE;UAAEhB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEF,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAE5B;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA;MACd,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAzB,KAAK,CAACmB,QAAQ,cAAAM,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBJ,IAAI,cAAAK,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI,cAAc;MACpEX,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACI,SAAS;QAC5BO,OAAO,EAAEe;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE2B;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAME,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACFb,QAAQ,CAAC;QAAEN,IAAI,EAAEV,YAAY,CAACC,WAAW;QAAEU,OAAO,EAAE;MAAK,CAAC,CAAC;MAC3DK,QAAQ,CAAC;QAAEN,IAAI,EAAEV,YAAY,CAACK;MAAY,CAAC,CAAC;MAE5C,MAAMa,QAAQ,GAAG,MAAMhC,OAAO,CAAC0C,QAAQ,CAACC,QAAQ,CAAC;MACjD,MAAM;QAAElC,IAAI;QAAEC;MAAM,CAAC,GAAGsB,QAAQ,CAACE,IAAI;MAErCjC,YAAY,CAACS,KAAK,CAAC;MACnBN,SAAS,CAACK,IAAI,CAAC;MAEfqB,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACE,aAAa;QAChCS,OAAO,EAAE;UAAEhB,IAAI;UAAEC;QAAM;MACzB,CAAC,CAAC;MAEF,OAAO;QAAE2B,OAAO,EAAE,IAAI;QAAE5B;MAAK,CAAC;IAChC,CAAC,CAAC,OAAOI,KAAK,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACd,MAAML,YAAY,GAAG,EAAAI,gBAAA,GAAA/B,KAAK,CAACmB,QAAQ,cAAAY,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBV,IAAI,cAAAW,qBAAA,uBAApBA,qBAAA,CAAsBJ,OAAO,KAAI,qBAAqB;MAC3EX,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACI,SAAS;QAC5BO,OAAO,EAAEe;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE2B;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMM,MAAM,GAAGA,CAAA,KAAM;IACnBzC,YAAY,CAAC,CAAC;IACdJ,YAAY,CAAC,IAAI,CAAC;IAClB6B,QAAQ,CAAC;MAAEN,IAAI,EAAEV,YAAY,CAACG;IAAO,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAM8B,aAAa,GAAG,MAAOC,WAAW,IAAK;IAC3C,IAAI;MACFlB,QAAQ,CAAC;QAAEN,IAAI,EAAEV,YAAY,CAACK;MAAY,CAAC,CAAC;MAE5C,MAAMa,QAAQ,GAAG,MAAMhC,OAAO,CAAC+C,aAAa,CAACC,WAAW,CAAC;MACzD,MAAMC,WAAW,GAAGjB,QAAQ,CAACE,IAAI,CAACzB,IAAI;MAEtCL,SAAS,CAAC6C,WAAW,CAAC;MACtBnB,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACM,WAAW;QAC9BK,OAAO,EAAEwB;MACX,CAAC,CAAC;MAEF,OAAO;QAAEZ,OAAO,EAAE,IAAI;QAAE5B,IAAI,EAAEwC;MAAY,CAAC;IAC7C,CAAC,CAAC,OAAOpC,KAAK,EAAE;MAAA,IAAAqC,gBAAA,EAAAC,qBAAA;MACd,MAAMX,YAAY,GAAG,EAAAU,gBAAA,GAAArC,KAAK,CAACmB,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,uBAAuB;MAC7EX,QAAQ,CAAC;QACPN,IAAI,EAAEV,YAAY,CAACI,SAAS;QAC5BO,OAAO,EAAEe;MACX,CAAC,CAAC;MACF,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAExB,KAAK,EAAE2B;MAAa,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAMY,UAAU,GAAGA,CAAA,KAAM;IACvBtB,QAAQ,CAAC;MAAEN,IAAI,EAAEV,YAAY,CAACK;IAAY,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMkC,KAAK,GAAG;IACZ,GAAG/B,KAAK;IACRa,KAAK;IACLO,QAAQ;IACRI,MAAM;IACNC,aAAa;IACbK;EACF,CAAC;EAED,oBACE7C,OAAA,CAACmB,WAAW,CAAC4B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAzB,QAAA,EAChCA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;;AAED;AAAA7B,EAAA,CAhJaF,YAAY;AAAAgC,EAAA,GAAZhC,YAAY;AAiJzB,OAAO,MAAMiC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAGjE,UAAU,CAAC6B,WAAW,CAAC;EACvC,IAAI,CAACoC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAQpB,eAAelC,WAAW;AAAC,IAAAiC,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}