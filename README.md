# Weather App - MER<PERSON> Stack

A modern, responsive weather application built with the MERN stack (MongoDB, Express.js, React.js, Node.js) featuring real-time weather data, user authentication, and a clean, intuitive interface.

## Features

- 🌤️ Real-time weather data for any city worldwide
- 🔐 User authentication system (register/login)
- ⭐ Favorite cities management for logged-in users
- 📱 Responsive design optimized for all devices
- 🌙 Dark/Light mode toggle
- 📊 5-day weather forecast
- 🔍 City search with autocomplete
- 📤 Shareable weather cards
- 🎨 Modern UI with smooth animations

## Tech Stack

### Frontend
- React.js with modern hooks and context API
- Styled-components for CSS-in-JS styling
- React Router for navigation
- Axios for API calls
- FontAwesome icons

### Backend
- Node.js with Express.js
- MongoDB with Mongoose ODM
- JWT for authentication
- bcryptjs for password hashing
- Express rate limiting and security middleware
- OpenWeatherMap API integration

## Prerequisites

Before running this application, make sure you have the following installed:
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- OpenWeatherMap API key

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd weather-app
```

2. Install dependencies for both frontend and backend:
```bash
npm run install-all
```

3. Set up environment variables:
   - Copy `backend/.env.example` to `backend/.env`
   - Update the environment variables with your values:
     - `MONGODB_URI`: Your MongoDB connection string
     - `OPENWEATHER_API_KEY`: Your OpenWeatherMap API key
     - `JWT_SECRET`: A secure secret for JWT tokens

4. Start the development servers:
```bash
npm run dev
```

This will start both the backend server (port 5000) and frontend development server (port 3000).

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/user` - Get current user (protected)

### Weather
- `GET /api/weather/current/:city` - Get current weather for a city
- `GET /api/weather/forecast/:city` - Get 5-day forecast for a city

### Favorites
- `GET /api/favorites` - Get user's favorite cities (protected)
- `POST /api/favorites` - Add city to favorites (protected)
- `DELETE /api/favorites/:id` - Remove city from favorites (protected)

## Environment Variables

### Backend (.env)
```
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/weather-app
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRE=7d
OPENWEATHER_API_KEY=your_openweathermap_api_key
CLIENT_URL=http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run server` - Start only the backend server
- `npm run client` - Start only the frontend development server
- `npm run build` - Build the frontend for production
- `npm run install-all` - Install dependencies for both frontend and backend

## Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- Rate limiting to prevent abuse
- CORS configuration
- Input validation and sanitization
- Helmet.js for security headers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.
