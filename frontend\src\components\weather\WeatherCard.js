import React from 'react';
import styled from 'styled-components';
import Card from '../common/Card';
import Button from '../common/Button';

const WeatherCardContainer = styled(Card)`
  max-width: 600px;
  margin: 0 auto;
  background: ${({ theme }) => theme.colors.gradient};
  color: white;
  border: none;
`;

const WeatherHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const LocationInfo = styled.div`
  h2 {
    font-size: ${({ theme }) => theme.fontSize['2xl']};
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
    color: white;
  }
  
  p {
    font-size: ${({ theme }) => theme.fontSize.sm};
    margin: 0;
    opacity: 0.9;
    color: white;
  }
`;

const Temperature = styled.div`
  text-align: right;
  
  .temp {
    font-size: 4rem;
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    margin: 0;
    line-height: 1;
    color: white;
  }
  
  .feels-like {
    font-size: ${({ theme }) => theme.fontSize.sm};
    opacity: 0.9;
    margin: 0;
    color: white;
  }
`;

const WeatherDescription = styled.div`
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  
  .icon {
    font-size: 4rem;
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }
  
  .description {
    font-size: ${({ theme }) => theme.fontSize.lg};
    font-weight: ${({ theme }) => theme.fontWeight.medium};
    text-transform: capitalize;
    margin: 0;
    color: white;
  }
`;

const WeatherDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const DetailItem = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.sm};
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  backdrop-filter: blur(10px);
  
  .label {
    font-size: ${({ theme }) => theme.fontSize.xs};
    opacity: 0.8;
    margin-bottom: ${({ theme }) => theme.spacing.xs};
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
  }
  
  .value {
    font-size: ${({ theme }) => theme.fontSize.lg};
    font-weight: ${({ theme }) => theme.fontWeight.semibold};
    margin: 0;
    color: white;
  }
`;

const SunTimes = styled.div`
  display: flex;
  justify-content: space-around;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  padding: ${({ theme }) => theme.spacing.md};
  background: rgba(255, 255, 255, 0.1);
  border-radius: ${({ theme }) => theme.borderRadius.md};
  backdrop-filter: blur(10px);
`;

const SunTime = styled.div`
  text-align: center;
  
  .icon {
    font-size: 2rem;
    margin-bottom: ${({ theme }) => theme.spacing.xs};
  }
  
  .label {
    font-size: ${({ theme }) => theme.fontSize.sm};
    opacity: 0.8;
    margin-bottom: ${({ theme }) => theme.spacing.xs};
    color: white;
  }
  
  .time {
    font-size: ${({ theme }) => theme.fontSize.base};
    font-weight: ${({ theme }) => theme.fontWeight.medium};
    margin: 0;
    color: white;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  justify-content: center;
  flex-wrap: wrap;
`;

const getWeatherIcon = (iconCode) => {
  const iconMap = {
    '01d': '☀️', '01n': '🌙',
    '02d': '⛅', '02n': '☁️',
    '03d': '☁️', '03n': '☁️',
    '04d': '☁️', '04n': '☁️',
    '09d': '🌧️', '09n': '🌧️',
    '10d': '🌦️', '10n': '🌧️',
    '11d': '⛈️', '11n': '⛈️',
    '13d': '❄️', '13n': '❄️',
    '50d': '🌫️', '50n': '🌫️',
  };
  return iconMap[iconCode] || '🌤️';
};

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const WeatherCard = ({ data, onAddToFavorites, onViewForecast, showActions = true }) => {
  const { location, current, units } = data;

  return (
    <WeatherCardContainer>
      <Card.Content>
        <WeatherHeader>
          <LocationInfo>
            <h2>{location.name}</h2>
            <p>{location.country}</p>
          </LocationInfo>
          <Temperature>
            <p className="temp">{current.temperature}{units.temperature}</p>
            <p className="feels-like">
              Feels like {current.feelsLike}{units.temperature}
            </p>
          </Temperature>
        </WeatherHeader>

        <WeatherDescription>
          <div className="icon">{getWeatherIcon(current.icon)}</div>
          <p className="description">{current.description}</p>
        </WeatherDescription>

        <WeatherDetails>
          <DetailItem>
            <div className="label">Humidity</div>
            <p className="value">{current.humidity}%</p>
          </DetailItem>
          <DetailItem>
            <div className="label">Wind</div>
            <p className="value">{current.windSpeed} {units.windSpeed}</p>
          </DetailItem>
          <DetailItem>
            <div className="label">Pressure</div>
            <p className="value">{current.pressure} {units.pressure}</p>
          </DetailItem>
          <DetailItem>
            <div className="label">Visibility</div>
            <p className="value">{current.visibility} {units.visibility}</p>
          </DetailItem>
        </WeatherDetails>

        <SunTimes>
          <SunTime>
            <div className="icon">🌅</div>
            <div className="label">Sunrise</div>
            <p className="time">{formatTime(location.sunrise)}</p>
          </SunTime>
          <SunTime>
            <div className="icon">🌇</div>
            <div className="label">Sunset</div>
            <p className="time">{formatTime(location.sunset)}</p>
          </SunTime>
        </SunTimes>

        {showActions && (
          <ActionButtons>
            {onViewForecast && (
              <Button 
                variant="outline" 
                onClick={() => onViewForecast(location.name)}
                style={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  color: 'white'
                }}
              >
                5-Day Forecast
              </Button>
            )}
            {onAddToFavorites && (
              <Button 
                variant="outline"
                onClick={() => onAddToFavorites({
                  city: location.name.toLowerCase(),
                  country: location.country.toLowerCase(),
                  displayName: `${location.name}, ${location.country}`,
                  coordinates: location.coordinates
                })}
                style={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  borderColor: 'rgba(255, 255, 255, 0.3)',
                  color: 'white'
                }}
              >
                ⭐ Add to Favorites
              </Button>
            )}
          </ActionButtons>
        )}
      </Card.Content>
    </WeatherCardContainer>
  );
};

export default WeatherCard;
