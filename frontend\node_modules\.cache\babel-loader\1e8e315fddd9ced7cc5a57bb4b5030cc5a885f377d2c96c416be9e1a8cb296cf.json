{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\components\\\\weather\\\\WeatherCard.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport Card from '../common/Card';\nimport Button from '../common/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WeatherCardContainer = styled(Card)`\n  max-width: 600px;\n  margin: 0 auto;\n  background: ${({\n  theme\n}) => theme.colors.gradient};\n  color: white;\n  border: none;\n`;\n_c = WeatherCardContainer;\nconst WeatherHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n`;\n_c2 = WeatherHeader;\nconst LocationInfo = styled.div`\n  h2 {\n    font-size: ${({\n  theme\n}) => theme.fontSize['2xl']};\n    font-weight: ${({\n  theme\n}) => theme.fontWeight.bold};\n    margin: 0 0 ${({\n  theme\n}) => theme.spacing.xs} 0;\n    color: white;\n  }\n  \n  p {\n    font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n    margin: 0;\n    opacity: 0.9;\n    color: white;\n  }\n`;\n_c3 = LocationInfo;\nconst Temperature = styled.div`\n  text-align: right;\n  \n  .temp {\n    font-size: 4rem;\n    font-weight: ${({\n  theme\n}) => theme.fontWeight.bold};\n    margin: 0;\n    line-height: 1;\n    color: white;\n  }\n  \n  .feels-like {\n    font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n    opacity: 0.9;\n    margin: 0;\n    color: white;\n  }\n`;\n_c4 = Temperature;\nconst WeatherDescription = styled.div`\n  text-align: center;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n  \n  .icon {\n    font-size: 4rem;\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n  \n  .description {\n    font-size: ${({\n  theme\n}) => theme.fontSize.lg};\n    font-weight: ${({\n  theme\n}) => theme.fontWeight.medium};\n    text-transform: capitalize;\n    margin: 0;\n    color: white;\n  }\n`;\n_c5 = WeatherDescription;\nconst WeatherDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: ${({\n  theme\n}) => theme.spacing.md};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n`;\n_c6 = WeatherDetails;\nconst DetailItem = styled.div`\n  text-align: center;\n  padding: ${({\n  theme\n}) => theme.spacing.sm};\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: ${({\n  theme\n}) => theme.borderRadius.md};\n  backdrop-filter: blur(10px);\n  \n  .label {\n    font-size: ${({\n  theme\n}) => theme.fontSize.xs};\n    opacity: 0.8;\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.xs};\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    color: white;\n  }\n  \n  .value {\n    font-size: ${({\n  theme\n}) => theme.fontSize.lg};\n    font-weight: ${({\n  theme\n}) => theme.fontWeight.semibold};\n    margin: 0;\n    color: white;\n  }\n`;\n_c7 = DetailItem;\nconst SunTimes = styled.div`\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n  padding: ${({\n  theme\n}) => theme.spacing.md};\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: ${({\n  theme\n}) => theme.borderRadius.md};\n  backdrop-filter: blur(10px);\n`;\n_c8 = SunTimes;\nconst SunTime = styled.div`\n  text-align: center;\n  \n  .icon {\n    font-size: 2rem;\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.xs};\n  }\n  \n  .label {\n    font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n    opacity: 0.8;\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.xs};\n    color: white;\n  }\n  \n  .time {\n    font-size: ${({\n  theme\n}) => theme.fontSize.base};\n    font-weight: ${({\n  theme\n}) => theme.fontWeight.medium};\n    margin: 0;\n    color: white;\n  }\n`;\n_c9 = SunTime;\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${({\n  theme\n}) => theme.spacing.sm};\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n_c0 = ActionButtons;\nconst getWeatherIcon = iconCode => {\n  const iconMap = {\n    '01d': '☀️',\n    '01n': '🌙',\n    '02d': '⛅',\n    '02n': '☁️',\n    '03d': '☁️',\n    '03n': '☁️',\n    '04d': '☁️',\n    '04n': '☁️',\n    '09d': '🌧️',\n    '09n': '🌧️',\n    '10d': '🌦️',\n    '10n': '🌧️',\n    '11d': '⛈️',\n    '11n': '⛈️',\n    '13d': '❄️',\n    '13n': '❄️',\n    '50d': '🌫️',\n    '50n': '🌫️'\n  };\n  return iconMap[iconCode] || '🌤️';\n};\nconst formatTime = timestamp => {\n  return new Date(timestamp).toLocaleTimeString([], {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nconst WeatherCard = ({\n  data,\n  onAddToFavorites,\n  onViewForecast,\n  showActions = true\n}) => {\n  const {\n    location,\n    current,\n    units\n  } = data;\n  return /*#__PURE__*/_jsxDEV(WeatherCardContainer, {\n    children: /*#__PURE__*/_jsxDEV(Card.Content, {\n      children: [/*#__PURE__*/_jsxDEV(WeatherHeader, {\n        children: [/*#__PURE__*/_jsxDEV(LocationInfo, {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: location.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: location.country\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Temperature, {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"temp\",\n            children: [current.temperature, units.temperature]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"feels-like\",\n            children: [\"Feels like \", current.feelsLike, units.temperature]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WeatherDescription, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"icon\",\n          children: getWeatherIcon(current.icon)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"description\",\n          children: current.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(WeatherDetails, {\n        children: [/*#__PURE__*/_jsxDEV(DetailItem, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Humidity\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"value\",\n            children: [current.humidity, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Wind\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"value\",\n            children: [current.windSpeed, \" \", units.windSpeed]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Pressure\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"value\",\n            children: [current.pressure, \" \", units.pressure]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DetailItem, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Visibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"value\",\n            children: [current.visibility, \" \", units.visibility]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SunTimes, {\n        children: [/*#__PURE__*/_jsxDEV(SunTime, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83C\\uDF05\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Sunrise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"time\",\n            children: formatTime(location.sunrise)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SunTime, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"icon\",\n            children: \"\\uD83C\\uDF07\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"label\",\n            children: \"Sunset\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"time\",\n            children: formatTime(location.sunset)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(ActionButtons, {\n        children: [onViewForecast && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => onViewForecast(location.name),\n          style: {\n            backgroundColor: 'rgba(255, 255, 255, 0.2)',\n            borderColor: 'rgba(255, 255, 255, 0.3)',\n            color: 'white'\n          },\n          children: \"5-Day Forecast\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 15\n        }, this), onAddToFavorites && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: () => onAddToFavorites({\n            city: location.name.toLowerCase(),\n            country: location.country.toLowerCase(),\n            displayName: `${location.name}, ${location.country}`,\n            coordinates: location.coordinates\n          }),\n          style: {\n            backgroundColor: 'rgba(255, 255, 255, 0.2)',\n            borderColor: 'rgba(255, 255, 255, 0.3)',\n            color: 'white'\n          },\n          children: \"\\u2B50 Add to Favorites\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_c1 = WeatherCard;\nexport default WeatherCard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"WeatherCardContainer\");\n$RefreshReg$(_c2, \"WeatherHeader\");\n$RefreshReg$(_c3, \"LocationInfo\");\n$RefreshReg$(_c4, \"Temperature\");\n$RefreshReg$(_c5, \"WeatherDescription\");\n$RefreshReg$(_c6, \"WeatherDetails\");\n$RefreshReg$(_c7, \"DetailItem\");\n$RefreshReg$(_c8, \"SunTimes\");\n$RefreshReg$(_c9, \"SunTime\");\n$RefreshReg$(_c0, \"ActionButtons\");\n$RefreshReg$(_c1, \"WeatherCard\");", "map": {"version": 3, "names": ["React", "styled", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WeatherCardContainer", "theme", "colors", "gradient", "_c", "WeatherHeader", "div", "spacing", "lg", "_c2", "LocationInfo", "fontSize", "fontWeight", "bold", "xs", "sm", "_c3", "Temperature", "_c4", "WeatherDescription", "medium", "_c5", "WeatherDetails", "md", "_c6", "DetailItem", "borderRadius", "semibold", "_c7", "SunTimes", "_c8", "SunTime", "base", "_c9", "ActionButtons", "_c0", "getWeatherIcon", "iconCode", "iconMap", "formatTime", "timestamp", "Date", "toLocaleTimeString", "hour", "minute", "WeatherCard", "data", "onAddToFavorites", "onViewForecast", "showActions", "location", "current", "units", "children", "Content", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "country", "className", "temperature", "feelsLike", "icon", "description", "humidity", "windSpeed", "pressure", "visibility", "sunrise", "sunset", "variant", "onClick", "style", "backgroundColor", "borderColor", "color", "city", "toLowerCase", "displayName", "coordinates", "_c1", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/components/weather/WeatherCard.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport Card from '../common/Card';\nimport Button from '../common/Button';\n\nconst WeatherCardContainer = styled(Card)`\n  max-width: 600px;\n  margin: 0 auto;\n  background: ${({ theme }) => theme.colors.gradient};\n  color: white;\n  border: none;\n`;\n\nconst WeatherHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst LocationInfo = styled.div`\n  h2 {\n    font-size: ${({ theme }) => theme.fontSize['2xl']};\n    font-weight: ${({ theme }) => theme.fontWeight.bold};\n    margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;\n    color: white;\n  }\n  \n  p {\n    font-size: ${({ theme }) => theme.fontSize.sm};\n    margin: 0;\n    opacity: 0.9;\n    color: white;\n  }\n`;\n\nconst Temperature = styled.div`\n  text-align: right;\n  \n  .temp {\n    font-size: 4rem;\n    font-weight: ${({ theme }) => theme.fontWeight.bold};\n    margin: 0;\n    line-height: 1;\n    color: white;\n  }\n  \n  .feels-like {\n    font-size: ${({ theme }) => theme.fontSize.sm};\n    opacity: 0.9;\n    margin: 0;\n    color: white;\n  }\n`;\n\nconst WeatherDescription = styled.div`\n  text-align: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n  \n  .icon {\n    font-size: 4rem;\n    margin-bottom: ${({ theme }) => theme.spacing.sm};\n  }\n  \n  .description {\n    font-size: ${({ theme }) => theme.fontSize.lg};\n    font-weight: ${({ theme }) => theme.fontWeight.medium};\n    text-transform: capitalize;\n    margin: 0;\n    color: white;\n  }\n`;\n\nconst WeatherDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst DetailItem = styled.div`\n  text-align: center;\n  padding: ${({ theme }) => theme.spacing.sm};\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  backdrop-filter: blur(10px);\n  \n  .label {\n    font-size: ${({ theme }) => theme.fontSize.xs};\n    opacity: 0.8;\n    margin-bottom: ${({ theme }) => theme.spacing.xs};\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    color: white;\n  }\n  \n  .value {\n    font-size: ${({ theme }) => theme.fontSize.lg};\n    font-weight: ${({ theme }) => theme.fontWeight.semibold};\n    margin: 0;\n    color: white;\n  }\n`;\n\nconst SunTimes = styled.div`\n  display: flex;\n  justify-content: space-around;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n  padding: ${({ theme }) => theme.spacing.md};\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  backdrop-filter: blur(10px);\n`;\n\nconst SunTime = styled.div`\n  text-align: center;\n  \n  .icon {\n    font-size: 2rem;\n    margin-bottom: ${({ theme }) => theme.spacing.xs};\n  }\n  \n  .label {\n    font-size: ${({ theme }) => theme.fontSize.sm};\n    opacity: 0.8;\n    margin-bottom: ${({ theme }) => theme.spacing.xs};\n    color: white;\n  }\n  \n  .time {\n    font-size: ${({ theme }) => theme.fontSize.base};\n    font-weight: ${({ theme }) => theme.fontWeight.medium};\n    margin: 0;\n    color: white;\n  }\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n  justify-content: center;\n  flex-wrap: wrap;\n`;\n\nconst getWeatherIcon = (iconCode) => {\n  const iconMap = {\n    '01d': '☀️', '01n': '🌙',\n    '02d': '⛅', '02n': '☁️',\n    '03d': '☁️', '03n': '☁️',\n    '04d': '☁️', '04n': '☁️',\n    '09d': '🌧️', '09n': '🌧️',\n    '10d': '🌦️', '10n': '🌧️',\n    '11d': '⛈️', '11n': '⛈️',\n    '13d': '❄️', '13n': '❄️',\n    '50d': '🌫️', '50n': '🌫️',\n  };\n  return iconMap[iconCode] || '🌤️';\n};\n\nconst formatTime = (timestamp) => {\n  return new Date(timestamp).toLocaleTimeString([], { \n    hour: '2-digit', \n    minute: '2-digit' \n  });\n};\n\nconst WeatherCard = ({ data, onAddToFavorites, onViewForecast, showActions = true }) => {\n  const { location, current, units } = data;\n\n  return (\n    <WeatherCardContainer>\n      <Card.Content>\n        <WeatherHeader>\n          <LocationInfo>\n            <h2>{location.name}</h2>\n            <p>{location.country}</p>\n          </LocationInfo>\n          <Temperature>\n            <p className=\"temp\">{current.temperature}{units.temperature}</p>\n            <p className=\"feels-like\">\n              Feels like {current.feelsLike}{units.temperature}\n            </p>\n          </Temperature>\n        </WeatherHeader>\n\n        <WeatherDescription>\n          <div className=\"icon\">{getWeatherIcon(current.icon)}</div>\n          <p className=\"description\">{current.description}</p>\n        </WeatherDescription>\n\n        <WeatherDetails>\n          <DetailItem>\n            <div className=\"label\">Humidity</div>\n            <p className=\"value\">{current.humidity}%</p>\n          </DetailItem>\n          <DetailItem>\n            <div className=\"label\">Wind</div>\n            <p className=\"value\">{current.windSpeed} {units.windSpeed}</p>\n          </DetailItem>\n          <DetailItem>\n            <div className=\"label\">Pressure</div>\n            <p className=\"value\">{current.pressure} {units.pressure}</p>\n          </DetailItem>\n          <DetailItem>\n            <div className=\"label\">Visibility</div>\n            <p className=\"value\">{current.visibility} {units.visibility}</p>\n          </DetailItem>\n        </WeatherDetails>\n\n        <SunTimes>\n          <SunTime>\n            <div className=\"icon\">🌅</div>\n            <div className=\"label\">Sunrise</div>\n            <p className=\"time\">{formatTime(location.sunrise)}</p>\n          </SunTime>\n          <SunTime>\n            <div className=\"icon\">🌇</div>\n            <div className=\"label\">Sunset</div>\n            <p className=\"time\">{formatTime(location.sunset)}</p>\n          </SunTime>\n        </SunTimes>\n\n        {showActions && (\n          <ActionButtons>\n            {onViewForecast && (\n              <Button \n                variant=\"outline\" \n                onClick={() => onViewForecast(location.name)}\n                style={{ \n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  borderColor: 'rgba(255, 255, 255, 0.3)',\n                  color: 'white'\n                }}\n              >\n                5-Day Forecast\n              </Button>\n            )}\n            {onAddToFavorites && (\n              <Button \n                variant=\"outline\"\n                onClick={() => onAddToFavorites({\n                  city: location.name.toLowerCase(),\n                  country: location.country.toLowerCase(),\n                  displayName: `${location.name}, ${location.country}`,\n                  coordinates: location.coordinates\n                })}\n                style={{ \n                  backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                  borderColor: 'rgba(255, 255, 255, 0.3)',\n                  color: 'white'\n                }}\n              >\n                ⭐ Add to Favorites\n              </Button>\n            )}\n          </ActionButtons>\n        )}\n      </Card.Content>\n    </WeatherCardContainer>\n  );\n};\n\nexport default WeatherCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,MAAM,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,oBAAoB,GAAGL,MAAM,CAACC,IAAI,CAAC;AACzC;AACA;AACA,gBAAgB,CAAC;EAAEK;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,QAAQ;AACpD;AACA;AACA,CAAC;AAACC,EAAA,GANIJ,oBAAoB;AAQ1B,MAAMK,aAAa,GAAGV,MAAM,CAACW,GAAG;AAChC;AACA;AACA;AACA,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACC,EAAE;AAClD,CAAC;AAACC,GAAA,GALIJ,aAAa;AAOnB,MAAMK,YAAY,GAAGf,MAAM,CAACW,GAAG;AAC/B;AACA,iBAAiB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;AACrD,mBAAmB,CAAC;EAAEV;AAAM,CAAC,KAAKA,KAAK,CAACW,UAAU,CAACC,IAAI;AACvD,kBAAkB,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACO,EAAE;AACjD;AACA;AACA;AACA;AACA,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACI,EAAE;AACjD;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAdIN,YAAY;AAgBlB,MAAMO,WAAW,GAAGtB,MAAM,CAACW,GAAG;AAC9B;AACA;AACA;AACA;AACA,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACW,UAAU,CAACC,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACI,EAAE;AACjD;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAjBID,WAAW;AAmBjB,MAAME,kBAAkB,GAAGxB,MAAM,CAACW,GAAG;AACrC;AACA,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACC,EAAE;AAClD;AACA;AACA;AACA,qBAAqB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACQ,EAAE;AACpD;AACA;AACA;AACA,iBAAiB,CAAC;EAAEd;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACH,EAAE;AACjD,mBAAmB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACW,UAAU,CAACQ,MAAM;AACzD;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAhBIF,kBAAkB;AAkBxB,MAAMG,cAAc,GAAG3B,MAAM,CAACW,GAAG;AACjC;AACA;AACA,SAAS,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACgB,EAAE;AACxC,mBAAmB,CAAC;EAAEtB;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACC,EAAE;AAClD,CAAC;AAACgB,GAAA,GALIF,cAAc;AAOpB,MAAMG,UAAU,GAAG9B,MAAM,CAACW,GAAG;AAC7B;AACA,aAAa,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACQ,EAAE;AAC5C;AACA,mBAAmB,CAAC;EAAEd;AAAM,CAAC,KAAKA,KAAK,CAACyB,YAAY,CAACH,EAAE;AACvD;AACA;AACA;AACA,iBAAiB,CAAC;EAAEtB;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACG,EAAE;AACjD;AACA,qBAAqB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACO,EAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACH,EAAE;AACjD,mBAAmB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACW,UAAU,CAACe,QAAQ;AAC3D;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAtBIH,UAAU;AAwBhB,MAAMI,QAAQ,GAAGlC,MAAM,CAACW,GAAG;AAC3B;AACA;AACA,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACC,EAAE;AAClD,aAAa,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACgB,EAAE;AAC5C;AACA,mBAAmB,CAAC;EAAEtB;AAAM,CAAC,KAAKA,KAAK,CAACyB,YAAY,CAACH,EAAE;AACvD;AACA,CAAC;AAACO,GAAA,GARID,QAAQ;AAUd,MAAME,OAAO,GAAGpC,MAAM,CAACW,GAAG;AAC1B;AACA;AACA;AACA;AACA,qBAAqB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACO,EAAE;AACpD;AACA;AACA;AACA,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACI,EAAE;AACjD;AACA,qBAAqB,CAAC;EAAEd;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACO,EAAE;AACpD;AACA;AACA;AACA;AACA,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACU,QAAQ,CAACqB,IAAI;AACnD,mBAAmB,CAAC;EAAE/B;AAAM,CAAC,KAAKA,KAAK,CAACW,UAAU,CAACQ,MAAM;AACzD;AACA;AACA;AACA,CAAC;AAACa,GAAA,GArBIF,OAAO;AAuBb,MAAMG,aAAa,GAAGvC,MAAM,CAACW,GAAG;AAChC;AACA,SAAS,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACQ,EAAE;AACxC;AACA;AACA,CAAC;AAACoB,GAAA,GALID,aAAa;AAOnB,MAAME,cAAc,GAAIC,QAAQ,IAAK;EACnC,MAAMC,OAAO,GAAG;IACd,KAAK,EAAE,IAAI;IAAE,KAAK,EAAE,IAAI;IACxB,KAAK,EAAE,GAAG;IAAE,KAAK,EAAE,IAAI;IACvB,KAAK,EAAE,IAAI;IAAE,KAAK,EAAE,IAAI;IACxB,KAAK,EAAE,IAAI;IAAE,KAAK,EAAE,IAAI;IACxB,KAAK,EAAE,KAAK;IAAE,KAAK,EAAE,KAAK;IAC1B,KAAK,EAAE,KAAK;IAAE,KAAK,EAAE,KAAK;IAC1B,KAAK,EAAE,IAAI;IAAE,KAAK,EAAE,IAAI;IACxB,KAAK,EAAE,IAAI;IAAE,KAAK,EAAE,IAAI;IACxB,KAAK,EAAE,KAAK;IAAE,KAAK,EAAE;EACvB,CAAC;EACD,OAAOA,OAAO,CAACD,QAAQ,CAAC,IAAI,KAAK;AACnC,CAAC;AAED,MAAME,UAAU,GAAIC,SAAS,IAAK;EAChC,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,kBAAkB,CAAC,EAAE,EAAE;IAChDC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC,IAAI;EAAEC,gBAAgB;EAAEC,cAAc;EAAEC,WAAW,GAAG;AAAK,CAAC,KAAK;EACtF,MAAM;IAAEC,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGN,IAAI;EAEzC,oBACE/C,OAAA,CAACC,oBAAoB;IAAAqD,QAAA,eACnBtD,OAAA,CAACH,IAAI,CAAC0D,OAAO;MAAAD,QAAA,gBACXtD,OAAA,CAACM,aAAa;QAAAgD,QAAA,gBACZtD,OAAA,CAACW,YAAY;UAAA2C,QAAA,gBACXtD,OAAA;YAAAsD,QAAA,EAAKH,QAAQ,CAACK;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxB5D,OAAA;YAAAsD,QAAA,EAAIH,QAAQ,CAACU;UAAO;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACf5D,OAAA,CAACkB,WAAW;UAAAoC,QAAA,gBACVtD,OAAA;YAAG8D,SAAS,EAAC,MAAM;YAAAR,QAAA,GAAEF,OAAO,CAACW,WAAW,EAAEV,KAAK,CAACU,WAAW;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChE5D,OAAA;YAAG8D,SAAS,EAAC,YAAY;YAAAR,QAAA,GAAC,aACb,EAACF,OAAO,CAACY,SAAS,EAAEX,KAAK,CAACU,WAAW;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEhB5D,OAAA,CAACoB,kBAAkB;QAAAkC,QAAA,gBACjBtD,OAAA;UAAK8D,SAAS,EAAC,MAAM;UAAAR,QAAA,EAAEjB,cAAc,CAACe,OAAO,CAACa,IAAI;QAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1D5D,OAAA;UAAG8D,SAAS,EAAC,aAAa;UAAAR,QAAA,EAAEF,OAAO,CAACc;QAAW;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAErB5D,OAAA,CAACuB,cAAc;QAAA+B,QAAA,gBACbtD,OAAA,CAAC0B,UAAU;UAAA4B,QAAA,gBACTtD,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5D,OAAA;YAAG8D,SAAS,EAAC,OAAO;YAAAR,QAAA,GAAEF,OAAO,CAACe,QAAQ,EAAC,GAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACb5D,OAAA,CAAC0B,UAAU;UAAA4B,QAAA,gBACTtD,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjC5D,OAAA;YAAG8D,SAAS,EAAC,OAAO;YAAAR,QAAA,GAAEF,OAAO,CAACgB,SAAS,EAAC,GAAC,EAACf,KAAK,CAACe,SAAS;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACb5D,OAAA,CAAC0B,UAAU;UAAA4B,QAAA,gBACTtD,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAQ;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrC5D,OAAA;YAAG8D,SAAS,EAAC,OAAO;YAAAR,QAAA,GAAEF,OAAO,CAACiB,QAAQ,EAAC,GAAC,EAAChB,KAAK,CAACgB,QAAQ;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACb5D,OAAA,CAAC0B,UAAU;UAAA4B,QAAA,gBACTtD,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAU;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC5D,OAAA;YAAG8D,SAAS,EAAC,OAAO;YAAAR,QAAA,GAAEF,OAAO,CAACkB,UAAU,EAAC,GAAC,EAACjB,KAAK,CAACiB,UAAU;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEjB5D,OAAA,CAAC8B,QAAQ;QAAAwB,QAAA,gBACPtD,OAAA,CAACgC,OAAO;UAAAsB,QAAA,gBACNtD,OAAA;YAAK8D,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5D,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpC5D,OAAA;YAAG8D,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAEd,UAAU,CAACW,QAAQ,CAACoB,OAAO;UAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACV5D,OAAA,CAACgC,OAAO;UAAAsB,QAAA,gBACNtD,OAAA;YAAK8D,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAC;UAAE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9B5D,OAAA;YAAK8D,SAAS,EAAC,OAAO;YAAAR,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnC5D,OAAA;YAAG8D,SAAS,EAAC,MAAM;YAAAR,QAAA,EAAEd,UAAU,CAACW,QAAQ,CAACqB,MAAM;UAAC;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEVV,WAAW,iBACVlD,OAAA,CAACmC,aAAa;QAAAmB,QAAA,GACXL,cAAc,iBACbjD,OAAA,CAACF,MAAM;UACL2E,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEA,CAAA,KAAMzB,cAAc,CAACE,QAAQ,CAACK,IAAI,CAAE;UAC7CmB,KAAK,EAAE;YACLC,eAAe,EAAE,0BAA0B;YAC3CC,WAAW,EAAE,0BAA0B;YACvCC,KAAK,EAAE;UACT,CAAE;UAAAxB,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EACAZ,gBAAgB,iBACfhD,OAAA,CAACF,MAAM;UACL2E,OAAO,EAAC,SAAS;UACjBC,OAAO,EAAEA,CAAA,KAAM1B,gBAAgB,CAAC;YAC9B+B,IAAI,EAAE5B,QAAQ,CAACK,IAAI,CAACwB,WAAW,CAAC,CAAC;YACjCnB,OAAO,EAAEV,QAAQ,CAACU,OAAO,CAACmB,WAAW,CAAC,CAAC;YACvCC,WAAW,EAAE,GAAG9B,QAAQ,CAACK,IAAI,KAAKL,QAAQ,CAACU,OAAO,EAAE;YACpDqB,WAAW,EAAE/B,QAAQ,CAAC+B;UACxB,CAAC,CAAE;UACHP,KAAK,EAAE;YACLC,eAAe,EAAE,0BAA0B;YAC3CC,WAAW,EAAE,0BAA0B;YACvCC,KAAK,EAAE;UACT,CAAE;UAAAxB,QAAA,EACH;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAChB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAE3B,CAAC;AAACuB,GAAA,GA9FIrC,WAAW;AAgGjB,eAAeA,WAAW;AAAC,IAAAzC,EAAA,EAAAK,GAAA,EAAAO,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAA+C,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAA1E,GAAA;AAAA0E,YAAA,CAAAnE,GAAA;AAAAmE,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAA9D,GAAA;AAAA8D,YAAA,CAAA3D,GAAA;AAAA2D,YAAA,CAAAvD,GAAA;AAAAuD,YAAA,CAAArD,GAAA;AAAAqD,YAAA,CAAAlD,GAAA;AAAAkD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}