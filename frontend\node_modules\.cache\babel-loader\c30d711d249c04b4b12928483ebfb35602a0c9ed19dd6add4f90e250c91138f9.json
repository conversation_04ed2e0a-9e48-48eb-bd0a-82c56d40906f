{"ast": null, "code": "import styled, { css } from 'styled-components';\nconst Card = styled.div`\n  background-color: ${({\n  theme\n}) => theme.colors.weatherCard};\n  border: 1px solid ${({\n  theme\n}) => theme.colors.border};\n  border-radius: ${({\n  theme\n}) => theme.borderRadius.lg};\n  padding: ${({\n  theme\n}) => theme.spacing.lg};\n  box-shadow: 0 1px 3px ${({\n  theme\n}) => theme.colors.shadow};\n  transition: all ${({\n  theme\n}) => theme.transitions.normal};\n  position: relative;\n  overflow: hidden;\n\n  /* Hover effect */\n  ${({\n  hoverable\n}) => hoverable && css`\n      cursor: pointer;\n      \n      &:hover {\n        background-color: ${({\n  theme\n}) => theme.colors.weatherCardHover};\n        box-shadow: 0 4px 12px ${({\n  theme\n}) => theme.colors.shadowHover};\n        transform: translateY(-2px);\n      }\n    `}\n\n  /* Clickable effect */\n  ${({\n  clickable\n}) => clickable && css`\n      cursor: pointer;\n      \n      &:active {\n        transform: translateY(0);\n        box-shadow: 0 1px 3px ${({\n  theme\n}) => theme.colors.shadow};\n      }\n    `}\n\n  /* Size variants */\n  ${({\n  size\n}) => {\n  switch (size) {\n    case 'sm':\n      return css`\n          padding: ${({\n        theme\n      }) => theme.spacing.md};\n        `;\n    case 'lg':\n      return css`\n          padding: ${({\n        theme\n      }) => theme.spacing.xl};\n        `;\n    default:\n      return '';\n  }\n}}\n\n  /* Gradient background */\n  ${({\n  gradient\n}) => gradient && css`\n      background: ${({\n  theme\n}) => theme.colors.gradient};\n      color: white;\n      border: none;\n      \n      * {\n        color: white;\n      }\n    `}\n\n  /* Loading state */\n  ${({\n  loading\n}) => loading && css`\n      position: relative;\n      \n      &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: linear-gradient(\n          90deg,\n          transparent,\n          ${({\n  theme\n}) => theme.colors.surface}50,\n          transparent\n        );\n        animation: shimmer 1.5s infinite;\n      }\n    `}\n\n  @keyframes shimmer {\n    0% {\n      transform: translateX(-100%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  }\n`;\nconst CardHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.md};\n  \n  h1, h2, h3, h4, h5, h6 {\n    margin: 0;\n    color: ${({\n  theme\n}) => theme.colors.text};\n  }\n`;\nconst CardTitle = styled.h3`\n  font-size: ${({\n  theme\n}) => theme.fontSize.lg};\n  font-weight: ${({\n  theme\n}) => theme.fontWeight.semibold};\n  color: ${({\n  theme\n}) => theme.colors.text};\n  margin: 0;\n`;\nconst CardSubtitle = styled.p`\n  font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n  color: ${({\n  theme\n}) => theme.colors.textSecondary};\n  margin: ${({\n  theme\n}) => theme.spacing.xs} 0 0 0;\n`;\nconst CardContent = styled.div`\n  flex: 1;\n`;\nconst CardFooter = styled.div`\n  margin-top: ${({\n  theme\n}) => theme.spacing.md};\n  padding-top: ${({\n  theme\n}) => theme.spacing.md};\n  border-top: 1px solid ${({\n  theme\n}) => theme.colors.border};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: ${({\n  theme\n}) => theme.spacing.sm};\n`;\nconst CardActions = styled.div`\n  display: flex;\n  gap: ${({\n  theme\n}) => theme.spacing.sm};\n  align-items: center;\n`;\n\n// Compound component exports\nCard.Header = CardHeader;\nCard.Title = CardTitle;\nCard.Subtitle = CardSubtitle;\nCard.Content = CardContent;\nCard.Footer = CardFooter;\nCard.Actions = CardActions;\nexport default Card;", "map": {"version": 3, "names": ["styled", "css", "Card", "div", "theme", "colors", "weatherCard", "border", "borderRadius", "lg", "spacing", "shadow", "transitions", "normal", "hoverable", "weatherCardHover", "shadowHover", "clickable", "size", "md", "xl", "gradient", "loading", "surface", "<PERSON><PERSON><PERSON><PERSON>", "text", "CardTitle", "h3", "fontSize", "fontWeight", "semibold", "CardSubtitle", "p", "sm", "textSecondary", "xs", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "CardActions", "Header", "Title", "Subtitle", "Content", "Footer", "Actions"], "sources": ["D:/weather-app/frontend/src/components/common/Card.js"], "sourcesContent": ["import styled, { css } from 'styled-components';\n\nconst Card = styled.div`\n  background-color: ${({ theme }) => theme.colors.weatherCard};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.lg};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: 0 1px 3px ${({ theme }) => theme.colors.shadow};\n  transition: all ${({ theme }) => theme.transitions.normal};\n  position: relative;\n  overflow: hidden;\n\n  /* Hover effect */\n  ${({ hoverable }) =>\n    hoverable &&\n    css`\n      cursor: pointer;\n      \n      &:hover {\n        background-color: ${({ theme }) => theme.colors.weatherCardHover};\n        box-shadow: 0 4px 12px ${({ theme }) => theme.colors.shadowHover};\n        transform: translateY(-2px);\n      }\n    `}\n\n  /* Clickable effect */\n  ${({ clickable }) =>\n    clickable &&\n    css`\n      cursor: pointer;\n      \n      &:active {\n        transform: translateY(0);\n        box-shadow: 0 1px 3px ${({ theme }) => theme.colors.shadow};\n      }\n    `}\n\n  /* Size variants */\n  ${({ size }) => {\n    switch (size) {\n      case 'sm':\n        return css`\n          padding: ${({ theme }) => theme.spacing.md};\n        `;\n      case 'lg':\n        return css`\n          padding: ${({ theme }) => theme.spacing.xl};\n        `;\n      default:\n        return '';\n    }\n  }}\n\n  /* Gradient background */\n  ${({ gradient }) =>\n    gradient &&\n    css`\n      background: ${({ theme }) => theme.colors.gradient};\n      color: white;\n      border: none;\n      \n      * {\n        color: white;\n      }\n    `}\n\n  /* Loading state */\n  ${({ loading }) =>\n    loading &&\n    css`\n      position: relative;\n      \n      &::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background: linear-gradient(\n          90deg,\n          transparent,\n          ${({ theme }) => theme.colors.surface}50,\n          transparent\n        );\n        animation: shimmer 1.5s infinite;\n      }\n    `}\n\n  @keyframes shimmer {\n    0% {\n      transform: translateX(-100%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  }\n`;\n\nconst CardHeader = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  \n  h1, h2, h3, h4, h5, h6 {\n    margin: 0;\n    color: ${({ theme }) => theme.colors.text};\n  }\n`;\n\nconst CardTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSize.lg};\n  font-weight: ${({ theme }) => theme.fontWeight.semibold};\n  color: ${({ theme }) => theme.colors.text};\n  margin: 0;\n`;\n\nconst CardSubtitle = styled.p`\n  font-size: ${({ theme }) => theme.fontSize.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0 0 0;\n`;\n\nconst CardContent = styled.div`\n  flex: 1;\n`;\n\nconst CardFooter = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  padding-top: ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CardActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n  align-items: center;\n`;\n\n// Compound component exports\nCard.Header = CardHeader;\nCard.Title = CardTitle;\nCard.Subtitle = CardSubtitle;\nCard.Content = CardContent;\nCard.Footer = CardFooter;\nCard.Actions = CardActions;\n\nexport default Card;\n"], "mappings": "AAAA,OAAOA,MAAM,IAAIC,GAAG,QAAQ,mBAAmB;AAE/C,MAAMC,IAAI,GAAGF,MAAM,CAACG,GAAG;AACvB,sBAAsB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,WAAW;AAC7D,sBAAsB,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,MAAM;AACxD,mBAAmB,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACI,YAAY,CAACC,EAAE;AACvD,aAAa,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACD,EAAE;AAC5C,0BAA0B,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,MAAM;AAC5D,oBAAoB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACQ,WAAW,CAACC,MAAM;AAC3D;AACA;AACA;AACA;AACA,IAAI,CAAC;EAAEC;AAAU,CAAC,KACdA,SAAS,IACTb,GAAG;AACP;AACA;AACA;AACA,4BAA4B,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACU,gBAAgB;AACxE,iCAAiC,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACW,WAAW;AACxE;AACA;AACA,KAAK;AACL;AACA;AACA,IAAI,CAAC;EAAEC;AAAU,CAAC,KACdA,SAAS,IACThB,GAAG;AACP;AACA;AACA;AACA;AACA,gCAAgC,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,MAAM;AAClE;AACA,KAAK;AACL;AACA;AACA,IAAI,CAAC;EAAEO;AAAK,CAAC,KAAK;EACd,QAAQA,IAAI;IACV,KAAK,IAAI;MACP,OAAOjB,GAAG;AAClB,qBAAqB,CAAC;QAAEG;MAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACS,EAAE;AACpD,SAAS;IACH,KAAK,IAAI;MACP,OAAOlB,GAAG;AAClB,qBAAqB,CAAC;QAAEG;MAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACU,EAAE;AACpD,SAAS;IACH;MACE,OAAO,EAAE;EACb;AACF,CAAC;AACH;AACA;AACA,IAAI,CAAC;EAAEC;AAAS,CAAC,KACbA,QAAQ,IACRpB,GAAG;AACP,oBAAoB,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACgB,QAAQ;AACxD;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,IAAI,CAAC;EAAEC;AAAQ,CAAC,KACZA,OAAO,IACPrB,GAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACkB,OAAO;AAC/C;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,UAAU,GAAGxB,MAAM,CAACG,GAAG;AAC7B;AACA;AACA;AACA,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACS,EAAE;AAClD;AACA;AACA;AACA,aAAa,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACoB,IAAI;AAC7C;AACA,CAAC;AAED,MAAMC,SAAS,GAAG1B,MAAM,CAAC2B,EAAE;AAC3B,eAAe,CAAC;EAAEvB;AAAM,CAAC,KAAKA,KAAK,CAACwB,QAAQ,CAACnB,EAAE;AAC/C,iBAAiB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACyB,UAAU,CAACC,QAAQ;AACzD,WAAW,CAAC;EAAE1B;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACoB,IAAI;AAC3C;AACA,CAAC;AAED,MAAMM,YAAY,GAAG/B,MAAM,CAACgC,CAAC;AAC7B,eAAe,CAAC;EAAE5B;AAAM,CAAC,KAAKA,KAAK,CAACwB,QAAQ,CAACK,EAAE;AAC/C,WAAW,CAAC;EAAE7B;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAAC6B,aAAa;AACpD,YAAY,CAAC;EAAE9B;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACyB,EAAE;AAC3C,CAAC;AAED,MAAMC,WAAW,GAAGpC,MAAM,CAACG,GAAG;AAC9B;AACA,CAAC;AAED,MAAMkC,UAAU,GAAGrC,MAAM,CAACG,GAAG;AAC7B,gBAAgB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACS,EAAE;AAC/C,iBAAiB,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACS,EAAE;AAChD,0BAA0B,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,MAAM;AAC5D;AACA;AACA;AACA,SAAS,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACuB,EAAE;AACxC,CAAC;AAED,MAAMK,WAAW,GAAGtC,MAAM,CAACG,GAAG;AAC9B;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACuB,EAAE;AACxC;AACA,CAAC;;AAED;AACA/B,IAAI,CAACqC,MAAM,GAAGf,UAAU;AACxBtB,IAAI,CAACsC,KAAK,GAAGd,SAAS;AACtBxB,IAAI,CAACuC,QAAQ,GAAGV,YAAY;AAC5B7B,IAAI,CAACwC,OAAO,GAAGN,WAAW;AAC1BlC,IAAI,CAACyC,MAAM,GAAGN,UAAU;AACxBnC,IAAI,CAAC0C,OAAO,GAAGN,WAAW;AAE1B,eAAepC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}