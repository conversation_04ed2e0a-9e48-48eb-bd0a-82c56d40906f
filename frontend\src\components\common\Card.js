import styled, { css } from 'styled-components';

const Card = styled.div`
  background-color: ${({ theme }) => theme.colors.weatherCard};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  padding: ${({ theme }) => theme.spacing.lg};
  box-shadow: 0 1px 3px ${({ theme }) => theme.colors.shadow};
  transition: all ${({ theme }) => theme.transitions.normal};
  position: relative;
  overflow: hidden;

  /* Hover effect */
  ${({ hoverable }) =>
    hoverable &&
    css`
      cursor: pointer;
      
      &:hover {
        background-color: ${({ theme }) => theme.colors.weatherCardHover};
        box-shadow: 0 4px 12px ${({ theme }) => theme.colors.shadowHover};
        transform: translateY(-2px);
      }
    `}

  /* Clickable effect */
  ${({ clickable }) =>
    clickable &&
    css`
      cursor: pointer;
      
      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px ${({ theme }) => theme.colors.shadow};
      }
    `}

  /* Size variants */
  ${({ size }) => {
    switch (size) {
      case 'sm':
        return css`
          padding: ${({ theme }) => theme.spacing.md};
        `;
      case 'lg':
        return css`
          padding: ${({ theme }) => theme.spacing.xl};
        `;
      default:
        return '';
    }
  }}

  /* Gradient background */
  ${({ gradient }) =>
    gradient &&
    css`
      background: ${({ theme }) => theme.colors.gradient};
      color: white;
      border: none;
      
      * {
        color: white;
      }
    `}

  /* Loading state */
  ${({ loading }) =>
    loading &&
    css`
      position: relative;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          transparent,
          ${({ theme }) => theme.colors.surface}50,
          transparent
        );
        animation: shimmer 1.5s infinite;
      }
    `}

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
`;

const CardHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  h1, h2, h3, h4, h5, h6 {
    margin: 0;
    color: ${({ theme }) => theme.colors.text};
  }
`;

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSize.lg};
  font-weight: ${({ theme }) => theme.fontWeight.semibold};
  color: ${({ theme }) => theme.colors.text};
  margin: 0;
`;

const CardSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSize.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  margin: ${({ theme }) => theme.spacing.xs} 0 0 0;
`;

const CardContent = styled.div`
  flex: 1;
`;

const CardFooter = styled.div`
  margin-top: ${({ theme }) => theme.spacing.md};
  padding-top: ${({ theme }) => theme.spacing.md};
  border-top: 1px solid ${({ theme }) => theme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: ${({ theme }) => theme.spacing.sm};
`;

const CardActions = styled.div`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  align-items: center;
`;

// Compound component exports
Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Subtitle = CardSubtitle;
Card.Content = CardContent;
Card.Footer = CardFooter;
Card.Actions = CardActions;

export default Card;
