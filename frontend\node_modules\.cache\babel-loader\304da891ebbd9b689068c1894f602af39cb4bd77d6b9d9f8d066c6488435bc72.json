{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\nconst SpinnerContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ${({\n  fullScreen\n}) => fullScreen ? '100vh' : '200px'};\n  flex-direction: column;\n  gap: ${({\n  theme\n}) => theme.spacing.md};\n`;\n_c = SpinnerContainer;\nconst Spinner = styled.div`\n  width: ${({\n  size\n}) => {\n  switch (size) {\n    case 'sm':\n      return '20px';\n    case 'lg':\n      return '60px';\n    default:\n      return '40px';\n  }\n}};\n  height: ${({\n  size\n}) => {\n  switch (size) {\n    case 'sm':\n      return '20px';\n    case 'lg':\n      return '60px';\n    default:\n      return '40px';\n  }\n}};\n  border: 3px solid ${({\n  theme\n}) => theme.colors.border};\n  border-top: 3px solid ${({\n  theme\n}) => theme.colors.primary};\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n`;\n_c2 = Spinner;\nconst LoadingText = styled.p`\n  color: ${({\n  theme\n}) => theme.colors.textSecondary};\n  font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n  margin: 0;\n`;\n_c3 = LoadingText;\nconst LoadingSpinner = ({\n  size = 'md',\n  text = 'Loading...',\n  fullScreen = false,\n  showText = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(SpinnerContainer, {\n    fullScreen: fullScreen,\n    children: [/*#__PURE__*/_jsxDEV(Spinner, {\n      size: size\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), showText && /*#__PURE__*/_jsxDEV(LoadingText, {\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 20\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_c4 = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SpinnerContainer\");\n$RefreshReg$(_c2, \"Spinner\");\n$RefreshReg$(_c3, \"LoadingText\");\n$RefreshReg$(_c4, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "jsxDEV", "_jsxDEV", "spin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "fullScreen", "theme", "spacing", "md", "_c", "Spinner", "size", "colors", "border", "primary", "_c2", "LoadingText", "p", "textSecondary", "fontSize", "sm", "_c3", "LoadingSpinner", "text", "showText", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/components/common/LoadingSpinner.js"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\n\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\n\nconst SpinnerContainer = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: ${({ fullScreen }) => (fullScreen ? '100vh' : '200px')};\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Spinner = styled.div`\n  width: ${({ size }) => {\n    switch (size) {\n      case 'sm': return '20px';\n      case 'lg': return '60px';\n      default: return '40px';\n    }\n  }};\n  height: ${({ size }) => {\n    switch (size) {\n      case 'sm': return '20px';\n      case 'lg': return '60px';\n      default: return '40px';\n    }\n  }};\n  border: 3px solid ${({ theme }) => theme.colors.border};\n  border-top: 3px solid ${({ theme }) => theme.colors.primary};\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n`;\n\nconst LoadingText = styled.p`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSize.sm};\n  margin: 0;\n`;\n\nconst LoadingSpinner = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  showText = true \n}) => {\n  return (\n    <SpinnerContainer fullScreen={fullScreen}>\n      <Spinner size={size} />\n      {showText && <LoadingText>{text}</LoadingText>}\n    </SpinnerContainer>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAMC,IAAI,GAAGH,SAAS;AACtB;AACA;AACA,CAAC;AAED,MAAMI,gBAAgB,GAAGL,MAAM,CAACM,GAAG;AACnC;AACA;AACA;AACA,gBAAgB,CAAC;EAAEC;AAAW,CAAC,KAAMA,UAAU,GAAG,OAAO,GAAG,OAAQ;AACpE;AACA,SAAS,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACC,EAAE;AACxC,CAAC;AAACC,EAAA,GAPIN,gBAAgB;AAStB,MAAMO,OAAO,GAAGZ,MAAM,CAACM,GAAG;AAC1B,WAAW,CAAC;EAAEO;AAAK,CAAC,KAAK;EACrB,QAAQA,IAAI;IACV,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH,YAAY,CAAC;EAAEA;AAAK,CAAC,KAAK;EACtB,QAAQA,IAAI;IACV,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB,KAAK,IAAI;MAAE,OAAO,MAAM;IACxB;MAAS,OAAO,MAAM;EACxB;AACF,CAAC;AACH,sBAAsB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,MAAM,CAACC,MAAM;AACxD,0BAA0B,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACM,MAAM,CAACE,OAAO;AAC7D;AACA,eAAeZ,IAAI;AACnB,CAAC;AAACa,GAAA,GAnBIL,OAAO;AAqBb,MAAMM,WAAW,GAAGlB,MAAM,CAACmB,CAAC;AAC5B,WAAW,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACM,MAAM,CAACM,aAAa;AACpD,eAAe,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACa,QAAQ,CAACC,EAAE;AAC/C;AACA,CAAC;AAACC,GAAA,GAJIL,WAAW;AAMjB,MAAMM,cAAc,GAAGA,CAAC;EACtBX,IAAI,GAAG,IAAI;EACXY,IAAI,GAAG,YAAY;EACnBlB,UAAU,GAAG,KAAK;EAClBmB,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,oBACEvB,OAAA,CAACE,gBAAgB;IAACE,UAAU,EAAEA,UAAW;IAAAoB,QAAA,gBACvCxB,OAAA,CAACS,OAAO;MAACC,IAAI,EAAEA;IAAK;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACtBL,QAAQ,iBAAIvB,OAAA,CAACe,WAAW;MAAAS,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9B,CAAC;AAEvB,CAAC;AAACC,GAAA,GAZIR,cAAc;AAcpB,eAAeA,cAAc;AAAC,IAAAb,EAAA,EAAAM,GAAA,EAAAM,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}