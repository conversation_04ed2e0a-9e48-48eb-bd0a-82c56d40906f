{"ast": null, "code": "/**\n * react-router-dom v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\n// index.ts\nimport { HydratedRouter, RouterProvider } from \"react-router/dom\";\nexport * from \"react-router\";\nexport { HydratedRouter, RouterProvider };", "map": {"version": 3, "names": ["HydratedRouter", "RouterProvider"], "sources": ["D:/weather-app/node_modules/react-router-dom/dist/index.mjs"], "sourcesContent": ["/**\n * react-router-dom v7.6.3\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\n// index.ts\nimport { HydratedRouter, RouterProvider } from \"react-router/dom\";\nexport * from \"react-router\";\nexport {\n  HydratedRouter,\n  RouterProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,cAAc,EAAEC,cAAc,QAAQ,kBAAkB;AACjE,cAAc,cAAc;AAC5B,SACED,cAAc,EACdC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}