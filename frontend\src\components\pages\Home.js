import React, { useState } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { weatherAPI } from '../../services/api';
import Button from '../common/Button';
import Card from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';
import WeatherCard from '../weather/WeatherCard';
import WeatherSearch from '../weather/WeatherSearch';
import ForecastCard from '../weather/ForecastCard';

const HomeContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
`;

const HeroSection = styled.section`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xxl} 0;
  background: ${({ theme }) => theme.colors.gradient};
  border-radius: ${({ theme }) => theme.borderRadius.xl};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  color: white;
`;

const HeroTitle = styled.h1`
  font-size: ${({ theme }) => theme.fontSize['4xl']};
  font-weight: ${({ theme }) => theme.fontWeight.bold};
  margin-bottom: ${({ theme }) => theme.spacing.md};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSize['2xl']};
  }
`;

const HeroSubtitle = styled.p`
  font-size: ${({ theme }) => theme.fontSize.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  opacity: 0.9;
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    font-size: ${({ theme }) => theme.fontSize.base};
  }
`;

const SearchSection = styled.section`
  max-width: 600px;
  margin: 0 auto ${({ theme }) => theme.spacing.xl};
`;

const SearchForm = styled.form`
  display: flex;
  gap: ${({ theme }) => theme.spacing.sm};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
  
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    flex-direction: column;
  }
`;

const SearchInput = styled(Input)`
  flex: 1;
`;

const WeatherSection = styled.section`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

const FeaturesSection = styled.section`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

const FeatureCard = styled(Card)`
  text-align: center;
`;

const FeatureIcon = styled.div`
  font-size: 3rem;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const CTASection = styled.section`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xl};
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.lg};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

const Home = () => {
  const [weatherData, setWeatherData] = useState(null);
  const [selectedCity, setSelectedCity] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showForecast, setShowForecast] = useState(false);
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  const handleCitySelect = async (city) => {
    setSelectedCity(city);
    setLoading(true);
    setError('');
    setShowForecast(false);

    try {
      const response = await weatherAPI.getCurrentWeather(city.name);
      setWeatherData(response.data.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch weather data');
      setWeatherData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleViewForecast = () => {
    setShowForecast(true);
  };

  const features = [
    {
      icon: '🌍',
      title: 'Global Weather',
      description: 'Get real-time weather data for any city worldwide with accurate forecasts.'
    },
    {
      icon: '⭐',
      title: 'Favorite Cities',
      description: 'Save your favorite locations and quickly access their weather information.'
    },
    {
      icon: '📱',
      title: 'Responsive Design',
      description: 'Perfect experience on desktop, tablet, and mobile devices.'
    },
    {
      icon: '🌙',
      title: 'Dark Mode',
      description: 'Switch between light and dark themes for comfortable viewing.'
    }
  ];

  return (
    <HomeContainer>
      <HeroSection>
        <HeroTitle>Welcome to WeatherApp</HeroTitle>
        <HeroSubtitle>
          Get accurate weather forecasts for any city around the world
        </HeroSubtitle>
        
        <SearchSection>
          <WeatherSearch
            onCitySelect={handleCitySelect}
            placeholder="Search for any city worldwide..."
            autoFocus={true}
          />
        </SearchSection>
      </HeroSection>

      {error && (
        <Card style={{ marginBottom: '2rem', borderColor: '#ef4444' }}>
          <Card.Content>
            <p style={{ color: '#ef4444', textAlign: 'center', margin: 0 }}>
              {error}
            </p>
          </Card.Content>
        </Card>
      )}

      {loading && (
        <WeatherSection>
          <LoadingSpinner text="Fetching weather data..." />
        </WeatherSection>
      )}

      {weatherData && !loading && (
        <WeatherSection>
          <WeatherCard data={weatherData} />
        </WeatherSection>
      )}

      <FeaturesSection>
        {features.map((feature, index) => (
          <FeatureCard key={index}>
            <Card.Content>
              <FeatureIcon>{feature.icon}</FeatureIcon>
              <Card.Title>{feature.title}</Card.Title>
              <Card.Subtitle>{feature.description}</Card.Subtitle>
            </Card.Content>
          </FeatureCard>
        ))}
      </FeaturesSection>

      {!isAuthenticated && (
        <CTASection>
          <h2 style={{ marginBottom: '1rem' }}>Ready to get started?</h2>
          <p style={{ marginBottom: '2rem', color: 'var(--text-secondary)' }}>
            Sign up now to save your favorite cities and get personalized weather updates.
          </p>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button onClick={() => navigate('/register')}>
              Sign Up Free
            </Button>
            <Button variant="outline" onClick={() => navigate('/login')}>
              Login
            </Button>
          </div>
        </CTASection>
      )}
    </HomeContainer>
  );
};

export default Home;
