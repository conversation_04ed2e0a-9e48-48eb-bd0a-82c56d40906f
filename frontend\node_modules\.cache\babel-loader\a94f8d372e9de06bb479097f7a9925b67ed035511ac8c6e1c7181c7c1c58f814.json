{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport GlobalStyles from './styles/GlobalStyles';\nimport Layout from './components/layout/Layout';\nimport Home from './components/pages/Home';\nimport Login from './components/pages/Login';\nimport Register from './components/pages/Register';\nimport Dashboard from './components/pages/Dashboard';\nimport Profile from './components/pages/Profile';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: [/*#__PURE__*/_jsxDEV(GlobalStyles, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/login\",\n              element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/register\",\n              element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/profile\",\n              element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                children: /*#__PURE__*/_jsxDEV(Profile, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 37,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "ThemeProvider", "GlobalStyles", "Layout", "Home", "<PERSON><PERSON>", "Register", "Dashboard", "Profile", "ProtectedRoute", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport GlobalStyles from './styles/GlobalStyles';\nimport Layout from './components/layout/Layout';\nimport Home from './components/pages/Home';\nimport Login from './components/pages/Login';\nimport Register from './components/pages/Register';\nimport Dashboard from './components/pages/Dashboard';\nimport Profile from './components/pages/Profile';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <GlobalStyles />\n        <Router>\n          <Layout>\n            <Routes>\n              <Route path=\"/\" element={<Home />} />\n              <Route path=\"/login\" element={<Login />} />\n              <Route path=\"/register\" element={<Register />} />\n              <Route\n                path=\"/dashboard\"\n                element={\n                  <ProtectedRoute>\n                    <Dashboard />\n                  </ProtectedRoute>\n                }\n              />\n              <Route\n                path=\"/profile\"\n                element={\n                  <ProtectedRoute>\n                    <Profile />\n                  </ProtectedRoute>\n                }\n              />\n              <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n            </Routes>\n          </Layout>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,OAAO,MAAM,4BAA4B;AAChD,OAAOC,cAAc,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,aAAa;IAAAY,QAAA,eACZF,OAAA,CAACX,YAAY;MAAAa,QAAA,gBACXF,OAAA,CAACT,YAAY;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChBN,OAAA,CAACf,MAAM;QAAAiB,QAAA,eACLF,OAAA,CAACR,MAAM;UAAAU,QAAA,eACLF,OAAA,CAACd,MAAM;YAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;cAACoB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAER,OAAA,CAACP,IAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrCN,OAAA,CAACb,KAAK;cAACoB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAER,OAAA,CAACN,KAAK;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3CN,OAAA,CAACb,KAAK;cAACoB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAER,OAAA,CAACL,QAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDN,OAAA,CAACb,KAAK;cACJoB,IAAI,EAAC,YAAY;cACjBC,OAAO,eACLR,OAAA,CAACF,cAAc;gBAAAI,QAAA,eACbF,OAAA,CAACJ,SAAS;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFN,OAAA,CAACb,KAAK;cACJoB,IAAI,EAAC,UAAU;cACfC,OAAO,eACLR,OAAA,CAACF,cAAc;gBAAAI,QAAA,eACbF,OAAA,CAACH,OAAO;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFN,OAAA,CAACb,KAAK;cAACoB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAER,OAAA,CAACZ,QAAQ;gBAACqB,EAAE,EAAC,GAAG;gBAACC,OAAO;cAAA;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACK,EAAA,GAlCQV,GAAG;AAoCZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}