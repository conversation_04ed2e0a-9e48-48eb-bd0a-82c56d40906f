{"ast": null, "code": "import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Auth API\nexport const authAPI = {\n  register: userData => api.post('/auth/register', userData),\n  login: credentials => api.post('/auth/login', credentials),\n  getUser: () => api.get('/auth/user'),\n  updateProfile: profileData => api.put('/auth/profile', profileData)\n};\n\n// Weather API\nexport const weatherAPI = {\n  getCurrentWeather: (city, units = 'metric') => api.get(`/weather/current/${encodeURIComponent(city)}?units=${units}`),\n  getForecast: (city, units = 'metric') => api.get(`/weather/forecast/${encodeURIComponent(city)}?units=${units}`),\n  searchCities: (query, limit = 5) => api.get(`/weather/search/${encodeURIComponent(query)}?limit=${limit}`)\n};\n\n// Favorites API\nexport const favoritesAPI = {\n  getFavorites: () => api.get('/favorites'),\n  addFavorite: cityData => api.post('/favorites', cityData),\n  updateFavorite: (id, updateData) => api.put(`/favorites/${id}`, updateData),\n  removeFavorite: id => api.delete(`/favorites/${id}`),\n  getFavorite: id => api.get(`/favorites/${id}`)\n};\n\n// Utility functions\nexport const setAuthToken = token => {\n  if (token) {\n    localStorage.setItem('token', token);\n    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n  } else {\n    localStorage.removeItem('token');\n    delete api.defaults.headers.common['Authorization'];\n  }\n};\nexport const getStoredToken = () => {\n  return localStorage.getItem('token');\n};\nexport const getStoredUser = () => {\n  const user = localStorage.getItem('user');\n  return user ? JSON.parse(user) : null;\n};\nexport const storeUser = user => {\n  localStorage.setItem('user', JSON.stringify(user));\n};\nexport const clearStorage = () => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "authAPI", "register", "userData", "post", "login", "credentials", "getUser", "get", "updateProfile", "profileData", "put", "weatherAPI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "city", "units", "encodeURIComponent", "getForecast", "searchCities", "query", "limit", "favoritesAPI", "getFavorites", "addFavorite", "cityData", "updateFavorite", "id", "updateData", "removeFavorite", "delete", "getFavorite", "setAuthToken", "setItem", "defaults", "common", "getStoredToken", "getStoredUser", "user", "JSON", "parse", "storeUser", "stringify", "clearStorage"], "sources": ["D:/weather-app/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\n// Create axios instance with base configuration\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || '/api',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      localStorage.removeItem('user');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Auth API\nexport const authAPI = {\n  register: (userData) => api.post('/auth/register', userData),\n  login: (credentials) => api.post('/auth/login', credentials),\n  getUser: () => api.get('/auth/user'),\n  updateProfile: (profileData) => api.put('/auth/profile', profileData),\n};\n\n// Weather API\nexport const weatherAPI = {\n  getCurrentWeather: (city, units = 'metric') => \n    api.get(`/weather/current/${encodeURIComponent(city)}?units=${units}`),\n  getForecast: (city, units = 'metric') => \n    api.get(`/weather/forecast/${encodeURIComponent(city)}?units=${units}`),\n  searchCities: (query, limit = 5) => \n    api.get(`/weather/search/${encodeURIComponent(query)}?limit=${limit}`),\n};\n\n// Favorites API\nexport const favoritesAPI = {\n  getFavorites: () => api.get('/favorites'),\n  addFavorite: (cityData) => api.post('/favorites', cityData),\n  updateFavorite: (id, updateData) => api.put(`/favorites/${id}`, updateData),\n  removeFavorite: (id) => api.delete(`/favorites/${id}`),\n  getFavorite: (id) => api.get(`/favorites/${id}`),\n};\n\n// Utility functions\nexport const setAuthToken = (token) => {\n  if (token) {\n    localStorage.setItem('token', token);\n    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n  } else {\n    localStorage.removeItem('token');\n    delete api.defaults.headers.common['Authorization'];\n  }\n};\n\nexport const getStoredToken = () => {\n  return localStorage.getItem('token');\n};\n\nexport const getStoredUser = () => {\n  const user = localStorage.getItem('user');\n  return user ? JSON.parse(user) : null;\n};\n\nexport const storeUser = (user) => {\n  localStorage.setItem('user', JSON.stringify(user));\n};\n\nexport const clearStorage = () => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,GAAG,GAAGD,KAAK,CAACE,MAAM,CAAC;EACvBC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,MAAM;EAChDC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAP,GAAG,CAACQ,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAhB,GAAG,CAACQ,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrBC,QAAQ,EAAGC,QAAQ,IAAK5B,GAAG,CAAC6B,IAAI,CAAC,gBAAgB,EAAED,QAAQ,CAAC;EAC5DE,KAAK,EAAGC,WAAW,IAAK/B,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAEE,WAAW,CAAC;EAC5DC,OAAO,EAAEA,CAAA,KAAMhC,GAAG,CAACiC,GAAG,CAAC,YAAY,CAAC;EACpCC,aAAa,EAAGC,WAAW,IAAKnC,GAAG,CAACoC,GAAG,CAAC,eAAe,EAAED,WAAW;AACtE,CAAC;;AAED;AACA,OAAO,MAAME,UAAU,GAAG;EACxBC,iBAAiB,EAAEA,CAACC,IAAI,EAAEC,KAAK,GAAG,QAAQ,KACxCxC,GAAG,CAACiC,GAAG,CAAC,oBAAoBQ,kBAAkB,CAACF,IAAI,CAAC,UAAUC,KAAK,EAAE,CAAC;EACxEE,WAAW,EAAEA,CAACH,IAAI,EAAEC,KAAK,GAAG,QAAQ,KAClCxC,GAAG,CAACiC,GAAG,CAAC,qBAAqBQ,kBAAkB,CAACF,IAAI,CAAC,UAAUC,KAAK,EAAE,CAAC;EACzEG,YAAY,EAAEA,CAACC,KAAK,EAAEC,KAAK,GAAG,CAAC,KAC7B7C,GAAG,CAACiC,GAAG,CAAC,mBAAmBQ,kBAAkB,CAACG,KAAK,CAAC,UAAUC,KAAK,EAAE;AACzE,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,YAAY,EAAEA,CAAA,KAAM/C,GAAG,CAACiC,GAAG,CAAC,YAAY,CAAC;EACzCe,WAAW,EAAGC,QAAQ,IAAKjD,GAAG,CAAC6B,IAAI,CAAC,YAAY,EAAEoB,QAAQ,CAAC;EAC3DC,cAAc,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAKpD,GAAG,CAACoC,GAAG,CAAC,cAAce,EAAE,EAAE,EAAEC,UAAU,CAAC;EAC3EC,cAAc,EAAGF,EAAE,IAAKnD,GAAG,CAACsD,MAAM,CAAC,cAAcH,EAAE,EAAE,CAAC;EACtDI,WAAW,EAAGJ,EAAE,IAAKnD,GAAG,CAACiC,GAAG,CAAC,cAAckB,EAAE,EAAE;AACjD,CAAC;;AAED;AACA,OAAO,MAAMK,YAAY,GAAI5C,KAAK,IAAK;EACrC,IAAIA,KAAK,EAAE;IACTC,YAAY,CAAC4C,OAAO,CAAC,OAAO,EAAE7C,KAAK,CAAC;IACpCZ,GAAG,CAAC0D,QAAQ,CAACnD,OAAO,CAACoD,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU/C,KAAK,EAAE;EAClE,CAAC,MAAM;IACLC,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOtB,GAAG,CAAC0D,QAAQ,CAACnD,OAAO,CAACoD,MAAM,CAAC,eAAe,CAAC;EACrD;AACF,CAAC;AAED,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,OAAO/C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;AAED,OAAO,MAAM+C,aAAa,GAAGA,CAAA,KAAM;EACjC,MAAMC,IAAI,GAAGjD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,OAAOgD,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,GAAG,IAAI;AACvC,CAAC;AAED,OAAO,MAAMG,SAAS,GAAIH,IAAI,IAAK;EACjCjD,YAAY,CAAC4C,OAAO,CAAC,MAAM,EAAEM,IAAI,CAACG,SAAS,CAACJ,IAAI,CAAC,CAAC;AACpD,CAAC;AAED,OAAO,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAChCtD,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;EAChCT,YAAY,CAACS,UAAU,CAAC,MAAM,CAAC;AACjC,CAAC;AAED,eAAetB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}