const express = require('express');
const { body, param, validationResult } = require('express-validator');
const Favorite = require('../models/Favorite');
const { auth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/favorites
// @desc    Get user's favorite cities
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const favorites = await Favorite.getUserFavorites(req.user._id);
    
    const formattedFavorites = favorites.map(favorite => favorite.toAPIResponse());

    res.json({
      success: true,
      count: favorites.length,
      data: formattedFavorites
    });

  } catch (error) {
    console.error('Get favorites error:', error);
    res.status(500).json({
      message: 'Failed to fetch favorite cities',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   POST /api/favorites
// @desc    Add city to favorites
// @access  Private
router.post('/', auth, [
  body('city')
    .trim()
    .isLength({ min: 1 })
    .withMessage('City name is required'),
  body('country')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Country is required'),
  body('displayName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Display name is required'),
  body('coordinates.lat')
    .isFloat({ min: -90, max: 90 })
    .withMessage('Latitude must be between -90 and 90'),
  body('coordinates.lon')
    .isFloat({ min: -180, max: 180 })
    .withMessage('Longitude must be between -180 and 180'),
  body('timezone')
    .optional()
    .isString(),
  body('isDefault')
    .optional()
    .isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { city, country, displayName, coordinates, timezone, isDefault } = req.body;

    // Check if city is already in favorites
    const existingFavorite = await Favorite.isFavorited(req.user._id, city, country);
    if (existingFavorite) {
      return res.status(400).json({
        message: 'City is already in your favorites'
      });
    }

    // If this is set as default, remove default from other favorites
    if (isDefault) {
      await Favorite.updateMany(
        { user: req.user._id },
        { $set: { isDefault: false } }
      );
    }

    // Create new favorite
    const favorite = new Favorite({
      user: req.user._id,
      city,
      country,
      displayName,
      coordinates,
      timezone,
      isDefault: isDefault || false
    });

    await favorite.save();

    res.status(201).json({
      success: true,
      message: 'City added to favorites successfully',
      data: favorite.toAPIResponse()
    });

  } catch (error) {
    console.error('Add favorite error:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        message: 'City is already in your favorites'
      });
    }

    res.status(500).json({
      message: 'Failed to add city to favorites',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   PUT /api/favorites/:id
// @desc    Update favorite city
// @access  Private
router.put('/:id', auth, [
  param('id').isMongoId().withMessage('Invalid favorite ID'),
  body('displayName')
    .optional()
    .trim()
    .isLength({ min: 1 })
    .withMessage('Display name cannot be empty'),
  body('isDefault')
    .optional()
    .isBoolean()
    .withMessage('isDefault must be a boolean'),
  body('timezone')
    .optional()
    .isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { displayName, isDefault, timezone } = req.body;

    // Find the favorite
    const favorite = await Favorite.findOne({ _id: id, user: req.user._id });
    if (!favorite) {
      return res.status(404).json({
        message: 'Favorite city not found'
      });
    }

    // If setting as default, remove default from other favorites
    if (isDefault === true) {
      await Favorite.updateMany(
        { user: req.user._id, _id: { $ne: id } },
        { $set: { isDefault: false } }
      );
    }

    // Update favorite
    if (displayName !== undefined) favorite.displayName = displayName;
    if (isDefault !== undefined) favorite.isDefault = isDefault;
    if (timezone !== undefined) favorite.timezone = timezone;

    await favorite.save();

    res.json({
      success: true,
      message: 'Favorite city updated successfully',
      data: favorite.toAPIResponse()
    });

  } catch (error) {
    console.error('Update favorite error:', error);
    res.status(500).json({
      message: 'Failed to update favorite city',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   DELETE /api/favorites/:id
// @desc    Remove city from favorites
// @access  Private
router.delete('/:id', auth, [
  param('id').isMongoId().withMessage('Invalid favorite ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;

    // Find and delete the favorite
    const favorite = await Favorite.findOneAndDelete({ _id: id, user: req.user._id });
    if (!favorite) {
      return res.status(404).json({
        message: 'Favorite city not found'
      });
    }

    res.json({
      success: true,
      message: 'City removed from favorites successfully'
    });

  } catch (error) {
    console.error('Delete favorite error:', error);
    res.status(500).json({
      message: 'Failed to remove city from favorites',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @route   GET /api/favorites/:id
// @desc    Get specific favorite city
// @access  Private
router.get('/:id', auth, [
  param('id').isMongoId().withMessage('Invalid favorite ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { id } = req.params;

    const favorite = await Favorite.findOne({ _id: id, user: req.user._id });
    if (!favorite) {
      return res.status(404).json({
        message: 'Favorite city not found'
      });
    }

    res.json({
      success: true,
      data: favorite.toAPIResponse()
    });

  } catch (error) {
    console.error('Get favorite error:', error);
    res.status(500).json({
      message: 'Failed to fetch favorite city',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;
