{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\components\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Card from '../common/Card';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 ${({\n  theme\n}) => theme.spacing.md};\n`;\n_c = DashboardContainer;\nconst WelcomeSection = styled.section`\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.xl};\n`;\n_c2 = WelcomeSection;\nconst Title = styled.h1`\n  color: ${({\n  theme\n}) => theme.colors.text};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.md};\n`;\n_c3 = Title;\nconst Subtitle = styled.p`\n  color: ${({\n  theme\n}) => theme.colors.textSecondary};\n  font-size: ${({\n  theme\n}) => theme.fontSize.lg};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n`;\n_c4 = Subtitle;\nconst ComingSoonCard = styled(Card)`\n  text-align: center;\n  padding: ${({\n  theme\n}) => theme.spacing.xxl};\n`;\n_c5 = ComingSoonCard;\nconst ComingSoonIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n`;\n_c6 = ComingSoonIcon;\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n    children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n        children: \"Your personalized weather dashboard is coming soon.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ComingSoonCard, {\n      children: /*#__PURE__*/_jsxDEV(Card.Content, {\n        children: [/*#__PURE__*/_jsxDEV(ComingSoonIcon, {\n          children: \"\\uD83D\\uDEA7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Title, {\n          children: \"Dashboard Coming Soon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Subtitle, {\n          children: \"We're working hard to bring you an amazing dashboard experience with:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '2rem',\n            textAlign: 'left',\n            maxWidth: '400px',\n            margin: '2rem auto 0'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              listStyle: 'none',\n              padding: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: \"\\u2B50 Your favorite cities\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: \"\\uD83D\\uDCCA Weather analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: \"\\uD83D\\uDD14 Weather alerts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: \"\\uD83D\\uDCF1 Personalized widgets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              style: {\n                marginBottom: '1rem'\n              },\n              children: \"\\uD83D\\uDCC8 Historical weather data\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            marginTop: '2rem',\n            color: 'var(--text-secondary)'\n          },\n          children: \"In the meantime, you can search for weather information on the home page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c7 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"DashboardContainer\");\n$RefreshReg$(_c2, \"WelcomeSection\");\n$RefreshReg$(_c3, \"Title\");\n$RefreshReg$(_c4, \"Subtitle\");\n$RefreshReg$(_c5, \"ComingSoonCard\");\n$RefreshReg$(_c6, \"ComingSoonIcon\");\n$RefreshReg$(_c7, \"Dashboard\");", "map": {"version": 3, "names": ["React", "styled", "useAuth", "Card", "jsxDEV", "_jsxDEV", "DashboardContainer", "div", "theme", "spacing", "md", "_c", "WelcomeSection", "section", "xl", "_c2", "Title", "h1", "colors", "text", "_c3", "Subtitle", "p", "textSecondary", "fontSize", "lg", "_c4", "ComingSoonCard", "xxl", "_c5", "ComingSoonIcon", "_c6", "Dashboard", "_s", "user", "children", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Content", "style", "marginTop", "textAlign", "max<PERSON><PERSON><PERSON>", "margin", "listStyle", "padding", "marginBottom", "color", "_c7", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/components/pages/Dashboard.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Card from '../common/Card';\n\nconst DashboardContainer = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 ${({ theme }) => theme.spacing.md};\n`;\n\nconst WelcomeSection = styled.section`\n  margin-bottom: ${({ theme }) => theme.spacing.xl};\n`;\n\nconst Title = styled.h1`\n  color: ${({ theme }) => theme.colors.text};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Subtitle = styled.p`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSize.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ComingSoonCard = styled(Card)`\n  text-align: center;\n  padding: ${({ theme }) => theme.spacing.xxl};\n`;\n\nconst ComingSoonIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n\n  return (\n    <DashboardContainer>\n      <WelcomeSection>\n        <Title>Welcome back, {user?.name}!</Title>\n        <Subtitle>\n          Your personalized weather dashboard is coming soon.\n        </Subtitle>\n      </WelcomeSection>\n\n      <ComingSoonCard>\n        <Card.Content>\n          <ComingSoonIcon>🚧</ComingSoonIcon>\n          <Card.Title>Dashboard Coming Soon</Card.Title>\n          <Card.Subtitle>\n            We're working hard to bring you an amazing dashboard experience with:\n          </Card.Subtitle>\n          <div style={{ marginTop: '2rem', textAlign: 'left', maxWidth: '400px', margin: '2rem auto 0' }}>\n            <ul style={{ listStyle: 'none', padding: 0 }}>\n              <li style={{ marginBottom: '1rem' }}>⭐ Your favorite cities</li>\n              <li style={{ marginBottom: '1rem' }}>📊 Weather analytics</li>\n              <li style={{ marginBottom: '1rem' }}>🔔 Weather alerts</li>\n              <li style={{ marginBottom: '1rem' }}>📱 Personalized widgets</li>\n              <li style={{ marginBottom: '1rem' }}>📈 Historical weather data</li>\n            </ul>\n          </div>\n          <p style={{ marginTop: '2rem', color: 'var(--text-secondary)' }}>\n            In the meantime, you can search for weather information on the home page.\n          </p>\n        </Card.Content>\n      </ComingSoonCard>\n    </DashboardContainer>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,IAAI,MAAM,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGL,MAAM,CAACM,GAAG;AACrC;AACA;AACA,eAAe,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACC,EAAE;AAC9C,CAAC;AAACC,EAAA,GAJIL,kBAAkB;AAMxB,MAAMM,cAAc,GAAGX,MAAM,CAACY,OAAO;AACrC,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACK,EAAE;AAClD,CAAC;AAACC,GAAA,GAFIH,cAAc;AAIpB,MAAMI,KAAK,GAAGf,MAAM,CAACgB,EAAE;AACvB,WAAW,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACU,MAAM,CAACC,IAAI;AAC3C,mBAAmB,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACC,EAAE;AAClD,CAAC;AAACU,GAAA,GAHIJ,KAAK;AAKX,MAAMK,QAAQ,GAAGpB,MAAM,CAACqB,CAAC;AACzB,WAAW,CAAC;EAAEd;AAAM,CAAC,KAAKA,KAAK,CAACU,MAAM,CAACK,aAAa;AACpD,eAAe,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACgB,QAAQ,CAACC,EAAE;AAC/C,mBAAmB,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACgB,EAAE;AAClD,CAAC;AAACC,GAAA,GAJIL,QAAQ;AAMd,MAAMM,cAAc,GAAG1B,MAAM,CAACE,IAAI,CAAC;AACnC;AACA,aAAa,CAAC;EAAEK;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACmB,GAAG;AAC7C,CAAC;AAACC,GAAA,GAHIF,cAAc;AAKpB,MAAMG,cAAc,GAAG7B,MAAM,CAACM,GAAG;AACjC;AACA,mBAAmB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,OAAO,CAACgB,EAAE;AAClD,CAAC;AAACM,GAAA,GAHID,cAAc;AAKpB,MAAME,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGhC,OAAO,CAAC,CAAC;EAE1B,oBACEG,OAAA,CAACC,kBAAkB;IAAA6B,QAAA,gBACjB9B,OAAA,CAACO,cAAc;MAAAuB,QAAA,gBACb9B,OAAA,CAACW,KAAK;QAAAmB,QAAA,GAAC,gBAAc,EAACD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,IAAI,EAAC,GAAC;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1CnC,OAAA,CAACgB,QAAQ;QAAAc,QAAA,EAAC;MAEV;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAEjBnC,OAAA,CAACsB,cAAc;MAAAQ,QAAA,eACb9B,OAAA,CAACF,IAAI,CAACsC,OAAO;QAAAN,QAAA,gBACX9B,OAAA,CAACyB,cAAc;UAAAK,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eACnCnC,OAAA,CAACF,IAAI,CAACa,KAAK;UAAAmB,QAAA,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC9CnC,OAAA,CAACF,IAAI,CAACkB,QAAQ;UAAAc,QAAA,EAAC;QAEf;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAChBnC,OAAA;UAAKqC,KAAK,EAAE;YAAEC,SAAS,EAAE,MAAM;YAAEC,SAAS,EAAE,MAAM;YAAEC,QAAQ,EAAE,OAAO;YAAEC,MAAM,EAAE;UAAc,CAAE;UAAAX,QAAA,eAC7F9B,OAAA;YAAIqC,KAAK,EAAE;cAAEK,SAAS,EAAE,MAAM;cAAEC,OAAO,EAAE;YAAE,CAAE;YAAAb,QAAA,gBAC3C9B,OAAA;cAAIqC,KAAK,EAAE;gBAAEO,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEnC,OAAA;cAAIqC,KAAK,EAAE;gBAAEO,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAoB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DnC,OAAA;cAAIqC,KAAK,EAAE;gBAAEO,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DnC,OAAA;cAAIqC,KAAK,EAAE;gBAAEO,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjEnC,OAAA;cAAIqC,KAAK,EAAE;gBAAEO,YAAY,EAAE;cAAO,CAAE;cAAAd,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnC,OAAA;UAAGqC,KAAK,EAAE;YAAEC,SAAS,EAAE,MAAM;YAAEO,KAAK,EAAE;UAAwB,CAAE;UAAAf,QAAA,EAAC;QAEjE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEzB,CAAC;AAACP,EAAA,CAnCID,SAAS;EAAA,QACI9B,OAAO;AAAA;AAAAiD,GAAA,GADpBnB,SAAS;AAqCf,eAAeA,SAAS;AAAC,IAAArB,EAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAoB,GAAA;AAAAC,YAAA,CAAAzC,EAAA;AAAAyC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}