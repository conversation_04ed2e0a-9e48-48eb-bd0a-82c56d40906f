{"ast": null, "code": "const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({\n      getCLS,\n      getFID,\n      getFCP,\n      getLCP,\n      getTTFB\n    }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\nexport default reportWebVitals;", "map": {"version": 3, "names": ["reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB"], "sources": ["D:/weather-app/frontend/src/reportWebVitals.js"], "sourcesContent": ["const reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n"], "mappings": "AAAA,MAAMA,eAAe,GAAGC,WAAW,IAAI;EACrC,IAAIA,WAAW,IAAIA,WAAW,YAAYC,QAAQ,EAAE;IAClD,MAAM,CAAC,YAAY,CAAC,CAACC,IAAI,CAAC,CAAC;MAA<PERSON>,MAAM;MAAEC,MAAM;MAA<PERSON>,MAAM;MAAEC,MAAM;MAAEC;IAAQ,CAAC,KAAK;MACzEJ,MAAM,CAACH,WAAW,CAAC;MACnBI,MAAM,CAACJ,WAAW,CAAC;MACnBK,MAAM,CAACL,WAAW,CAAC;MACnBM,MAAM,CAACN,WAAW,CAAC;MACnBO,OAAO,CAACP,WAAW,CAAC;IACtB,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}