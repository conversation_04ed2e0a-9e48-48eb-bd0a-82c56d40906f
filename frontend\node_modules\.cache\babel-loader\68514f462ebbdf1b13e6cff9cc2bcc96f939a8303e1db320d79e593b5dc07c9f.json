{"ast": null, "code": "import styled, { css } from 'styled-components';\nconst buttonVariants = {\n  primary: css`\n    background-color: ${({\n    theme\n  }) => theme.colors.primary};\n    color: white;\n    border: 1px solid ${({\n    theme\n  }) => theme.colors.primary};\n\n    &:hover:not(:disabled) {\n      background-color: ${({\n    theme\n  }) => theme.colors.primaryHover};\n      border-color: ${({\n    theme\n  }) => theme.colors.primaryHover};\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px ${({\n    theme\n  }) => theme.colors.shadow};\n    }\n  `,\n  secondary: css`\n    background-color: transparent;\n    color: ${({\n    theme\n  }) => theme.colors.text};\n    border: 1px solid ${({\n    theme\n  }) => theme.colors.border};\n\n    &:hover:not(:disabled) {\n      background-color: ${({\n    theme\n  }) => theme.colors.surface};\n      border-color: ${({\n    theme\n  }) => theme.colors.borderHover};\n    }\n  `,\n  outline: css`\n    background-color: transparent;\n    color: ${({\n    theme\n  }) => theme.colors.primary};\n    border: 1px solid ${({\n    theme\n  }) => theme.colors.primary};\n\n    &:hover:not(:disabled) {\n      background-color: ${({\n    theme\n  }) => theme.colors.primary};\n      color: white;\n    }\n  `,\n  ghost: css`\n    background-color: transparent;\n    color: ${({\n    theme\n  }) => theme.colors.text};\n    border: 1px solid transparent;\n\n    &:hover:not(:disabled) {\n      background-color: ${({\n    theme\n  }) => theme.colors.surface};\n    }\n  `,\n  danger: css`\n    background-color: ${({\n    theme\n  }) => theme.colors.error};\n    color: white;\n    border: 1px solid ${({\n    theme\n  }) => theme.colors.error};\n\n    &:hover:not(:disabled) {\n      background-color: #dc2626;\n      border-color: #dc2626;\n    }\n  `\n};\nconst buttonSizes = {\n  sm: css`\n    padding: ${({\n    theme\n  }) => theme.spacing.xs} ${({\n    theme\n  }) => theme.spacing.sm};\n    font-size: ${({\n    theme\n  }) => theme.fontSize.sm};\n    border-radius: ${({\n    theme\n  }) => theme.borderRadius.sm};\n  `,\n  md: css`\n    padding: ${({\n    theme\n  }) => theme.spacing.sm} ${({\n    theme\n  }) => theme.spacing.md};\n    font-size: ${({\n    theme\n  }) => theme.fontSize.base};\n    border-radius: ${({\n    theme\n  }) => theme.borderRadius.md};\n  `,\n  lg: css`\n    padding: ${({\n    theme\n  }) => theme.spacing.md} ${({\n    theme\n  }) => theme.spacing.lg};\n    font-size: ${({\n    theme\n  }) => theme.fontSize.lg};\n    border-radius: ${({\n    theme\n  }) => theme.borderRadius.lg};\n  `\n};\nconst Button = styled.button`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: ${({\n  theme\n}) => theme.spacing.xs};\n  font-weight: ${({\n  theme\n}) => theme.fontWeight.medium};\n  transition: all ${({\n  theme\n}) => theme.transitions.fast};\n  cursor: pointer;\n  text-decoration: none;\n  white-space: nowrap;\n  user-select: none;\n  position: relative;\n  overflow: hidden;\n\n  /* Apply variant styles */\n  ${({\n  variant = 'primary'\n}) => buttonVariants[variant]}\n\n  /* Apply size styles */\n  ${({\n  size = 'md'\n}) => buttonSizes[size]}\n\n  /* Full width */\n  ${({\n  fullWidth\n}) => fullWidth && css`\n      width: 100%;\n    `}\n\n  /* Loading state */\n  ${({\n  loading\n}) => loading && css`\n      cursor: not-allowed;\n      opacity: 0.7;\n    `}\n\n  /* Disabled state */\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.5;\n    transform: none !important;\n    box-shadow: none !important;\n  }\n\n  /* Focus state */\n  &:focus-visible {\n    outline: 2px solid ${({\n  theme\n}) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Icon only button */\n  ${({\n  iconOnly\n}) => iconOnly && css`\n      padding: ${({\n  theme\n}) => theme.spacing.sm};\n      aspect-ratio: 1;\n    `}\n\n  /* Loading spinner */\n  .spinner {\n    width: 16px;\n    height: 16px;\n    border: 2px solid transparent;\n    border-top: 2px solid currentColor;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\nexport default Button;", "map": {"version": 3, "names": ["styled", "css", "buttonVariants", "primary", "theme", "colors", "primaryHover", "shadow", "secondary", "text", "border", "surface", "borderHover", "outline", "ghost", "danger", "error", "buttonSizes", "sm", "spacing", "xs", "fontSize", "borderRadius", "md", "base", "lg", "<PERSON><PERSON>", "button", "fontWeight", "medium", "transitions", "fast", "variant", "size", "fullWidth", "loading", "iconOnly"], "sources": ["D:/weather-app/frontend/src/components/common/Button.js"], "sourcesContent": ["import styled, { css } from 'styled-components';\n\nconst buttonVariants = {\n  primary: css`\n    background-color: ${({ theme }) => theme.colors.primary};\n    color: white;\n    border: 1px solid ${({ theme }) => theme.colors.primary};\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primaryHover};\n      border-color: ${({ theme }) => theme.colors.primaryHover};\n      transform: translateY(-1px);\n      box-shadow: 0 4px 12px ${({ theme }) => theme.colors.shadow};\n    }\n  `,\n  secondary: css`\n    background-color: transparent;\n    color: ${({ theme }) => theme.colors.text};\n    border: 1px solid ${({ theme }) => theme.colors.border};\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.surface};\n      border-color: ${({ theme }) => theme.colors.borderHover};\n    }\n  `,\n  outline: css`\n    background-color: transparent;\n    color: ${({ theme }) => theme.colors.primary};\n    border: 1px solid ${({ theme }) => theme.colors.primary};\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primary};\n      color: white;\n    }\n  `,\n  ghost: css`\n    background-color: transparent;\n    color: ${({ theme }) => theme.colors.text};\n    border: 1px solid transparent;\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.surface};\n    }\n  `,\n  danger: css`\n    background-color: ${({ theme }) => theme.colors.error};\n    color: white;\n    border: 1px solid ${({ theme }) => theme.colors.error};\n\n    &:hover:not(:disabled) {\n      background-color: #dc2626;\n      border-color: #dc2626;\n    }\n  `,\n};\n\nconst buttonSizes = {\n  sm: css`\n    padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n    font-size: ${({ theme }) => theme.fontSize.sm};\n    border-radius: ${({ theme }) => theme.borderRadius.sm};\n  `,\n  md: css`\n    padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n    font-size: ${({ theme }) => theme.fontSize.base};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n  `,\n  lg: css`\n    padding: ${({ theme }) => theme.spacing.md} ${({ theme }) => theme.spacing.lg};\n    font-size: ${({ theme }) => theme.fontSize.lg};\n    border-radius: ${({ theme }) => theme.borderRadius.lg};\n  `,\n};\n\nconst Button = styled.button`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: ${({ theme }) => theme.spacing.xs};\n  font-weight: ${({ theme }) => theme.fontWeight.medium};\n  transition: all ${({ theme }) => theme.transitions.fast};\n  cursor: pointer;\n  text-decoration: none;\n  white-space: nowrap;\n  user-select: none;\n  position: relative;\n  overflow: hidden;\n\n  /* Apply variant styles */\n  ${({ variant = 'primary' }) => buttonVariants[variant]}\n\n  /* Apply size styles */\n  ${({ size = 'md' }) => buttonSizes[size]}\n\n  /* Full width */\n  ${({ fullWidth }) =>\n    fullWidth &&\n    css`\n      width: 100%;\n    `}\n\n  /* Loading state */\n  ${({ loading }) =>\n    loading &&\n    css`\n      cursor: not-allowed;\n      opacity: 0.7;\n    `}\n\n  /* Disabled state */\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.5;\n    transform: none !important;\n    box-shadow: none !important;\n  }\n\n  /* Focus state */\n  &:focus-visible {\n    outline: 2px solid ${({ theme }) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Icon only button */\n  ${({ iconOnly }) =>\n    iconOnly &&\n    css`\n      padding: ${({ theme }) => theme.spacing.sm};\n      aspect-ratio: 1;\n    `}\n\n  /* Loading spinner */\n  .spinner {\n    width: 16px;\n    height: 16px;\n    border: 2px solid transparent;\n    border-top: 2px solid currentColor;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nexport default Button;\n"], "mappings": "AAAA,OAAOA,MAAM,IAAIC,GAAG,QAAQ,mBAAmB;AAE/C,MAAMC,cAAc,GAAG;EACrBC,OAAO,EAAEF,GAAG;AACd,wBAAwB,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAC3D;AACA,wBAAwB,CAAC;IAAEC;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAC3D;AACA;AACA,0BAA0B,CAAC;IAAEC;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,YAAY;AAClE,sBAAsB,CAAC;IAAEF;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,YAAY;AAC9D;AACA,+BAA+B,CAAC;IAAEF;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,MAAM;AACjE;AACA,GAAG;EACDC,SAAS,EAAEP,GAAG;AAChB;AACA,aAAa,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,IAAI;AAC7C,wBAAwB,CAAC;IAAEL;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACK,MAAM;AAC1D;AACA;AACA,0BAA0B,CAAC;IAAEN;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,OAAO;AAC7D,sBAAsB,CAAC;IAAEP;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACO,WAAW;AAC7D;AACA,GAAG;EACDC,OAAO,EAAEZ,GAAG;AACd;AACA,aAAa,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAChD,wBAAwB,CAAC;IAAEC;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAC3D;AACA;AACA,0BAA0B,CAAC;IAAEC;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAC7D;AACA;AACA,GAAG;EACDW,KAAK,EAAEb,GAAG;AACZ;AACA,aAAa,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACI,IAAI;AAC7C;AACA;AACA;AACA,0BAA0B,CAAC;IAAEL;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,OAAO;AAC7D;AACA,GAAG;EACDI,MAAM,EAAEd,GAAG;AACb,wBAAwB,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACW,KAAK;AACzD;AACA,wBAAwB,CAAC;IAAEZ;EAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACW,KAAK;AACzD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,WAAW,GAAG;EAClBC,EAAE,EAAEjB,GAAG;AACT,eAAe,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACC,EAAE,IAAI,CAAC;IAAEhB;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACD,EAAE;AACjF,iBAAiB,CAAC;IAAEd;EAAM,CAAC,KAAKA,KAAK,CAACiB,QAAQ,CAACH,EAAE;AACjD,qBAAqB,CAAC;IAAEd;EAAM,CAAC,KAAKA,KAAK,CAACkB,YAAY,CAACJ,EAAE;AACzD,GAAG;EACDK,EAAE,EAAEtB,GAAG;AACT,eAAe,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACD,EAAE,IAAI,CAAC;IAAEd;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACI,EAAE;AACjF,iBAAiB,CAAC;IAAEnB;EAAM,CAAC,KAAKA,KAAK,CAACiB,QAAQ,CAACG,IAAI;AACnD,qBAAqB,CAAC;IAAEpB;EAAM,CAAC,KAAKA,KAAK,CAACkB,YAAY,CAACC,EAAE;AACzD,GAAG;EACDE,EAAE,EAAExB,GAAG;AACT,eAAe,CAAC;IAAEG;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACI,EAAE,IAAI,CAAC;IAAEnB;EAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACM,EAAE;AACjF,iBAAiB,CAAC;IAAErB;EAAM,CAAC,KAAKA,KAAK,CAACiB,QAAQ,CAACI,EAAE;AACjD,qBAAqB,CAAC;IAAErB;EAAM,CAAC,KAAKA,KAAK,CAACkB,YAAY,CAACG,EAAE;AACzD;AACA,CAAC;AAED,MAAMC,MAAM,GAAG1B,MAAM,CAAC2B,MAAM;AAC5B;AACA;AACA;AACA,SAAS,CAAC;EAAEvB;AAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACC,EAAE;AACxC,iBAAiB,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACwB,UAAU,CAACC,MAAM;AACvD,oBAAoB,CAAC;EAAEzB;AAAM,CAAC,KAAKA,KAAK,CAAC0B,WAAW,CAACC,IAAI;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;EAAEC,OAAO,GAAG;AAAU,CAAC,KAAK9B,cAAc,CAAC8B,OAAO,CAAC;AACxD;AACA;AACA,IAAI,CAAC;EAAEC,IAAI,GAAG;AAAK,CAAC,KAAKhB,WAAW,CAACgB,IAAI,CAAC;AAC1C;AACA;AACA,IAAI,CAAC;EAAEC;AAAU,CAAC,KACdA,SAAS,IACTjC,GAAG;AACP;AACA,KAAK;AACL;AACA;AACA,IAAI,CAAC;EAAEkC;AAAQ,CAAC,KACZA,OAAO,IACPlC,GAAG;AACP;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACF,OAAO;AAC5D;AACA;AACA;AACA;AACA,IAAI,CAAC;EAAEiC;AAAS,CAAC,KACbA,QAAQ,IACRnC,GAAG;AACP,iBAAiB,CAAC;EAAEG;AAAM,CAAC,KAAKA,KAAK,CAACe,OAAO,CAACD,EAAE;AAChD;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAeQ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}