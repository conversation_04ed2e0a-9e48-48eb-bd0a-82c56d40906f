import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { weatherAPI } from '../../services/api';
import Card from '../common/Card';
import LoadingSpinner from '../common/LoadingSpinner';

const ForecastContainer = styled(Card)`
  margin-top: ${({ theme }) => theme.spacing.lg};
`;

const ForecastGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const DayCard = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  text-align: center;
  transition: all ${({ theme }) => theme.transitions.fast};
  
  &:hover {
    background-color: ${({ theme }) => theme.colors.surfaceHover};
    transform: translateY(-2px);
    box-shadow: 0 4px 12px ${({ theme }) => theme.colors.shadow};
  }
`;

const DayName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeight.semibold};
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  font-size: ${({ theme }) => theme.fontSize.sm};
`;

const WeatherIcon = styled.div`
  font-size: 2.5rem;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const Temperature = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
  
  .high {
    font-weight: ${({ theme }) => theme.fontWeight.bold};
    color: ${({ theme }) => theme.colors.text};
    font-size: ${({ theme }) => theme.fontSize.lg};
  }
  
  .low {
    color: ${({ theme }) => theme.colors.textSecondary};
    font-size: ${({ theme }) => theme.fontSize.base};
  }
`;

const Description = styled.div`
  font-size: ${({ theme }) => theme.fontSize.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  text-transform: capitalize;
  margin-bottom: ${({ theme }) => theme.spacing.sm};
`;

const Details = styled.div`
  display: flex;
  justify-content: space-between;
  font-size: ${({ theme }) => theme.fontSize.xs};
  color: ${({ theme }) => theme.colors.textMuted};
  
  span {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
  }
`;

const ErrorMessage = styled.div`
  background-color: ${({ theme }) => theme.colors.error}20;
  border: 1px solid ${({ theme }) => theme.colors.error};
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  text-align: center;
  margin-top: ${({ theme }) => theme.spacing.md};
`;

const getWeatherIcon = (iconCode) => {
  const iconMap = {
    '01d': '☀️', '01n': '🌙',
    '02d': '⛅', '02n': '☁️',
    '03d': '☁️', '03n': '☁️',
    '04d': '☁️', '04n': '☁️',
    '09d': '🌧️', '09n': '🌧️',
    '10d': '🌦️', '10n': '🌧️',
    '11d': '⛈️', '11n': '⛈️',
    '13d': '❄️', '13n': '❄️',
    '50d': '🌫️', '50n': '🌫️',
  };
  return iconMap[iconCode] || '🌤️';
};

const formatDate = (date) => {
  const today = new Date();
  const forecastDate = new Date(date);
  
  if (forecastDate.toDateString() === today.toDateString()) {
    return 'Today';
  }
  
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  if (forecastDate.toDateString() === tomorrow.toDateString()) {
    return 'Tomorrow';
  }
  
  return forecastDate.toLocaleDateString([], { 
    weekday: 'short',
    month: 'short',
    day: 'numeric'
  });
};

const ForecastCard = ({ cityName, units = 'metric' }) => {
  const [forecastData, setForecastData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!cityName) return;

    const fetchForecast = async () => {
      setLoading(true);
      setError('');
      
      try {
        const response = await weatherAPI.getForecast(cityName, units);
        setForecastData(response.data.data);
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to fetch forecast data');
        setForecastData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchForecast();
  }, [cityName, units]);

  if (!cityName) return null;

  return (
    <ForecastContainer>
      <Card.Header>
        <Card.Title>5-Day Forecast</Card.Title>
        <Card.Subtitle>
          {forecastData?.location?.name}, {forecastData?.location?.country}
        </Card.Subtitle>
      </Card.Header>
      
      <Card.Content>
        {loading && (
          <LoadingSpinner text="Loading forecast..." />
        )}
        
        {error && (
          <ErrorMessage>{error}</ErrorMessage>
        )}
        
        {forecastData && !loading && (
          <ForecastGrid>
            {forecastData.forecast.map((day, index) => (
              <DayCard key={index}>
                <DayName>{formatDate(day.date)}</DayName>
                <WeatherIcon>{getWeatherIcon(day.icon)}</WeatherIcon>
                <Temperature>
                  <span className="high">
                    {day.temperature.max}{forecastData.units.temperature}
                  </span>
                  <span className="low">
                    {day.temperature.min}{forecastData.units.temperature}
                  </span>
                </Temperature>
                <Description>{day.description}</Description>
                <Details>
                  <span>
                    <div>💧</div>
                    <div>{day.humidity}%</div>
                  </span>
                  <span>
                    <div>💨</div>
                    <div>{day.windSpeed}{forecastData.units.windSpeed}</div>
                  </span>
                </Details>
              </DayCard>
            ))}
          </ForecastGrid>
        )}
      </Card.Content>
    </ForecastContainer>
  );
};

export default ForecastCard;
