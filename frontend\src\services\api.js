import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/auth/register', userData),
  login: (credentials) => api.post('/auth/login', credentials),
  getUser: () => api.get('/auth/user'),
  updateProfile: (profileData) => api.put('/auth/profile', profileData),
};

// Weather API
export const weatherAPI = {
  getCurrentWeather: (city, units = 'metric') => 
    api.get(`/weather/current/${encodeURIComponent(city)}?units=${units}`),
  getForecast: (city, units = 'metric') => 
    api.get(`/weather/forecast/${encodeURIComponent(city)}?units=${units}`),
  searchCities: (query, limit = 5) => 
    api.get(`/weather/search/${encodeURIComponent(query)}?limit=${limit}`),
};

// Favorites API
export const favoritesAPI = {
  getFavorites: () => api.get('/favorites'),
  addFavorite: (cityData) => api.post('/favorites', cityData),
  updateFavorite: (id, updateData) => api.put(`/favorites/${id}`, updateData),
  removeFavorite: (id) => api.delete(`/favorites/${id}`),
  getFavorite: (id) => api.get(`/favorites/${id}`),
};

// Utility functions
export const setAuthToken = (token) => {
  if (token) {
    localStorage.setItem('token', token);
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  } else {
    localStorage.removeItem('token');
    delete api.defaults.headers.common['Authorization'];
  }
};

export const getStoredToken = () => {
  return localStorage.getItem('token');
};

export const getStoredUser = () => {
  const user = localStorage.getItem('user');
  return user ? JSON.parse(user) : null;
};

export const storeUser = (user) => {
  localStorage.setItem('user', JSON.stringify(user));
};

export const clearStorage = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('user');
};

export default api;
