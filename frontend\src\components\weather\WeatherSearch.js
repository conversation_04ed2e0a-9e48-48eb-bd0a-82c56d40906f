import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { weatherAPI } from '../../services/api';
import Input from '../common/Input';
import LoadingSpinner from '../common/LoadingSpinner';

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
`;

const SearchResults = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-top: none;
  border-radius: 0 0 ${({ theme }) => theme.borderRadius.md} ${({ theme }) => theme.borderRadius.md};
  box-shadow: 0 4px 12px ${({ theme }) => theme.colors.shadow};
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  display: ${({ show }) => (show ? 'block' : 'none')};
`;

const SearchResultItem = styled.div`
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  cursor: pointer;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
  transition: background-color ${({ theme }) => theme.transitions.fast};
  
  &:hover, &.highlighted {
    background-color: ${({ theme }) => theme.colors.surface};
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const CityName = styled.div`
  font-weight: ${({ theme }) => theme.fontWeight.medium};
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;

const CityDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSize.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NoResults = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  text-align: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSize.sm};
`;

const LoadingContainer = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  display: flex;
  justify-content: center;
`;

const WeatherSearch = ({ 
  onCitySelect, 
  placeholder = "Search for a city...",
  autoFocus = false 
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [error, setError] = useState('');
  
  const searchRef = useRef(null);
  const resultsRef = useRef(null);
  const debounceRef = useRef(null);

  // Debounced search function
  const searchCities = async (searchQuery) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setResults([]);
      setShowResults(false);
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await weatherAPI.searchCities(searchQuery, 8);
      setResults(response.data.data);
      setShowResults(true);
      setHighlightedIndex(-1);
    } catch (err) {
      setError('Failed to search cities');
      setResults([]);
      setShowResults(false);
    } finally {
      setLoading(false);
    }
  };

  // Handle input change with debouncing
  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);

    // Clear previous debounce
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Set new debounce
    debounceRef.current = setTimeout(() => {
      searchCities(value);
    }, 300);
  };

  // Handle city selection
  const handleCitySelect = (city) => {
    setQuery(city.displayName);
    setShowResults(false);
    setHighlightedIndex(-1);
    onCitySelect(city);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showResults || results.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : results.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && highlightedIndex < results.length) {
          handleCitySelect(results[highlightedIndex]);
        }
        break;
      case 'Escape':
        setShowResults(false);
        setHighlightedIndex(-1);
        break;
      default:
        break;
    }
  };

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        searchRef.current && 
        !searchRef.current.contains(event.target) &&
        resultsRef.current &&
        !resultsRef.current.contains(event.target)
      ) {
        setShowResults(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Cleanup debounce on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, []);

  return (
    <SearchContainer ref={searchRef}>
      <Input
        type="text"
        placeholder={placeholder}
        value={query}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onFocus={() => {
          if (results.length > 0) {
            setShowResults(true);
          }
        }}
        autoFocus={autoFocus}
        error={error}
      />
      
      <SearchResults show={showResults} ref={resultsRef}>
        {loading && (
          <LoadingContainer>
            <LoadingSpinner size="sm" showText={false} />
          </LoadingContainer>
        )}
        
        {!loading && results.length === 0 && query.length >= 2 && (
          <NoResults>
            No cities found for "{query}"
          </NoResults>
        )}
        
        {!loading && results.length > 0 && results.map((city, index) => (
          <SearchResultItem
            key={`${city.name}-${city.country}-${index}`}
            className={index === highlightedIndex ? 'highlighted' : ''}
            onClick={() => handleCitySelect(city)}
          >
            <CityName>{city.name}</CityName>
            <CityDetails>
              {city.state && `${city.state}, `}{city.country}
            </CityDetails>
          </SearchResultItem>
        ))}
      </SearchResults>
    </SearchContainer>
  );
};

export default WeatherSearch;
