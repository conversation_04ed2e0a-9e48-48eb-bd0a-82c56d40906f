{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\components\\\\layout\\\\Footer.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FooterContainer = styled.footer`\n  background-color: ${({\n  theme\n}) => theme.colors.surface};\n  border-top: 1px solid ${({\n  theme\n}) => theme.colors.border};\n  padding: ${({\n  theme\n}) => theme.spacing.lg} 0;\n  margin-top: auto;\n`;\n_c = FooterContainer;\nconst FooterContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 ${({\n  theme\n}) => theme.spacing.md};\n  text-align: center;\n`;\n_c2 = FooterContent;\nconst FooterText = styled.p`\n  color: ${({\n  theme\n}) => theme.colors.textSecondary};\n  font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n  margin: 0;\n`;\n_c3 = FooterText;\nconst FooterLinks = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: ${({\n  theme\n}) => theme.spacing.lg};\n  margin-bottom: ${({\n  theme\n}) => theme.spacing.md};\n  \n  @media (max-width: ${({\n  theme\n}) => theme.breakpoints.sm}) {\n    flex-direction: column;\n    gap: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n`;\n_c4 = FooterLinks;\nconst FooterLink = styled.a`\n  color: ${({\n  theme\n}) => theme.colors.textSecondary};\n  text-decoration: none;\n  font-size: ${({\n  theme\n}) => theme.fontSize.sm};\n  transition: color ${({\n  theme\n}) => theme.transitions.fast};\n  \n  &:hover {\n    color: ${({\n  theme\n}) => theme.colors.primary};\n  }\n`;\n_c5 = FooterLink;\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(FooterContainer, {\n    children: /*#__PURE__*/_jsxDEV(FooterContent, {\n      children: [/*#__PURE__*/_jsxDEV(FooterLinks, {\n        children: [/*#__PURE__*/_jsxDEV(FooterLink, {\n          href: \"#\",\n          onClick: e => e.preventDefault(),\n          children: \"About\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n          href: \"#\",\n          onClick: e => e.preventDefault(),\n          children: \"Privacy Policy\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n          href: \"#\",\n          onClick: e => e.preventDefault(),\n          children: \"Terms of Service\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FooterLink, {\n          href: \"#\",\n          onClick: e => e.preventDefault(),\n          children: \"Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FooterText, {\n        children: \"\\xA9 2025 WeatherApp. All rights reserved. Powered by OpenWeatherMap API.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c6 = Footer;\nexport default Footer;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"FooterContainer\");\n$RefreshReg$(_c2, \"FooterContent\");\n$RefreshReg$(_c3, \"FooterText\");\n$RefreshReg$(_c4, \"FooterLinks\");\n$RefreshReg$(_c5, \"FooterLink\");\n$RefreshReg$(_c6, \"Footer\");", "map": {"version": 3, "names": ["React", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer", "theme", "colors", "surface", "border", "spacing", "lg", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "md", "_c2", "FooterText", "p", "textSecondary", "fontSize", "sm", "_c3", "FooterLinks", "breakpoints", "_c4", "FooterLink", "a", "transitions", "fast", "primary", "_c5", "Footer", "children", "href", "onClick", "e", "preventDefault", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c6", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/components/layout/Footer.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\n\nconst FooterContainer = styled.footer`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  padding: ${({ theme }) => theme.spacing.lg} 0;\n  margin-top: auto;\n`;\n\nconst FooterContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 ${({ theme }) => theme.spacing.md};\n  text-align: center;\n`;\n\nconst FooterText = styled.p`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSize.sm};\n  margin: 0;\n`;\n\nconst FooterLinks = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: ${({ theme }) => theme.spacing.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  \n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    flex-direction: column;\n    gap: ${({ theme }) => theme.spacing.sm};\n  }\n`;\n\nconst FooterLink = styled.a`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  text-decoration: none;\n  font-size: ${({ theme }) => theme.fontSize.sm};\n  transition: color ${({ theme }) => theme.transitions.fast};\n  \n  &:hover {\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\nconst Footer = () => {\n  return (\n    <FooterContainer>\n      <FooterContent>\n        <FooterLinks>\n          <FooterLink href=\"#\" onClick={(e) => e.preventDefault()}>\n            About\n          </FooterLink>\n          <FooterLink href=\"#\" onClick={(e) => e.preventDefault()}>\n            Privacy Policy\n          </FooterLink>\n          <FooterLink href=\"#\" onClick={(e) => e.preventDefault()}>\n            Terms of Service\n          </FooterLink>\n          <FooterLink href=\"#\" onClick={(e) => e.preventDefault()}>\n            Contact\n          </FooterLink>\n        </FooterLinks>\n        <FooterText>\n          © 2025 WeatherApp. All rights reserved. Powered by OpenWeatherMap API.\n        </FooterText>\n      </FooterContent>\n    </FooterContainer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,eAAe,GAAGH,MAAM,CAACI,MAAM;AACrC,sBAAsB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,OAAO;AACzD,0BAA0B,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,MAAM;AAC5D,aAAa,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAACC,EAAE;AAC5C;AACA,CAAC;AAACC,EAAA,GALIR,eAAe;AAOrB,MAAMS,aAAa,GAAGZ,MAAM,CAACa,GAAG;AAChC;AACA;AACA,eAAe,CAAC;EAAER;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAACK,EAAE;AAC9C;AACA,CAAC;AAACC,GAAA,GALIH,aAAa;AAOnB,MAAMI,UAAU,GAAGhB,MAAM,CAACiB,CAAC;AAC3B,WAAW,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACY,aAAa;AACpD,eAAe,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,QAAQ,CAACC,EAAE;AAC/C;AACA,CAAC;AAACC,GAAA,GAJIL,UAAU;AAMhB,MAAMM,WAAW,GAAGtB,MAAM,CAACa,GAAG;AAC9B;AACA;AACA,SAAS,CAAC;EAAER;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAACC,EAAE;AACxC,mBAAmB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAACK,EAAE;AAClD;AACA,uBAAuB,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACkB,WAAW,CAACH,EAAE;AAC1D;AACA,WAAW,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACI,OAAO,CAACW,EAAE;AAC1C;AACA,CAAC;AAACI,GAAA,GAVIF,WAAW;AAYjB,MAAMG,UAAU,GAAGzB,MAAM,CAAC0B,CAAC;AAC3B,WAAW,CAAC;EAAErB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACY,aAAa;AACpD;AACA,eAAe,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,QAAQ,CAACC,EAAE;AAC/C,sBAAsB,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACsB,WAAW,CAACC,IAAI;AAC3D;AACA;AACA,aAAa,CAAC;EAAEvB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACuB,OAAO;AAChD;AACA,CAAC;AAACC,GAAA,GATIL,UAAU;AAWhB,MAAMM,MAAM,GAAGA,CAAA,KAAM;EACnB,oBACE7B,OAAA,CAACC,eAAe;IAAA6B,QAAA,eACd9B,OAAA,CAACU,aAAa;MAAAoB,QAAA,gBACZ9B,OAAA,CAACoB,WAAW;QAAAU,QAAA,gBACV9B,OAAA,CAACuB,UAAU;UAACQ,IAAI,EAAC,GAAG;UAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAAJ,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACuB,UAAU;UAACQ,IAAI,EAAC,GAAG;UAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAAJ,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACuB,UAAU;UAACQ,IAAI,EAAC,GAAG;UAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAAJ,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtC,OAAA,CAACuB,UAAU;UAACQ,IAAI,EAAC,GAAG;UAACC,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,cAAc,CAAC,CAAE;UAAAJ,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdtC,OAAA,CAACc,UAAU;QAAAgB,QAAA,EAAC;MAEZ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACC,GAAA,GAxBIV,MAAM;AA0BZ,eAAeA,MAAM;AAAC,IAAApB,EAAA,EAAAI,GAAA,EAAAM,GAAA,EAAAG,GAAA,EAAAM,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}