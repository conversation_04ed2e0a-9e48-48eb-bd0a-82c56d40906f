{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\contexts\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider as StyledThemeProvider } from 'styled-components';\n\n// Theme definitions\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const lightTheme = {\n  name: 'light',\n  colors: {\n    primary: '#3B82F6',\n    primaryHover: '#2563EB',\n    secondary: '#6B7280',\n    background: '#FFFFFF',\n    surface: '#F9FAFB',\n    surfaceHover: '#F3F4F6',\n    text: '#111827',\n    textSecondary: '#6B7280',\n    textMuted: '#9CA3AF',\n    border: '#E5E7EB',\n    borderHover: '#D1D5DB',\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    info: '#3B82F6',\n    shadow: 'rgba(0, 0, 0, 0.1)',\n    shadowHover: 'rgba(0, 0, 0, 0.15)',\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    weatherCard: '#FFFFFF',\n    weatherCardHover: '#F8FAFC'\n  },\n  spacing: {\n    xs: '0.25rem',\n    sm: '0.5rem',\n    md: '1rem',\n    lg: '1.5rem',\n    xl: '2rem',\n    xxl: '3rem'\n  },\n  borderRadius: {\n    sm: '0.375rem',\n    md: '0.5rem',\n    lg: '0.75rem',\n    xl: '1rem',\n    full: '9999px'\n  },\n  fontSize: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem',\n    '4xl': '2.25rem'\n  },\n  fontWeight: {\n    normal: '400',\n    medium: '500',\n    semibold: '600',\n    bold: '700'\n  },\n  breakpoints: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px'\n  },\n  transitions: {\n    fast: '0.15s ease-in-out',\n    normal: '0.3s ease-in-out',\n    slow: '0.5s ease-in-out'\n  }\n};\nexport const darkTheme = {\n  ...lightTheme,\n  name: 'dark',\n  colors: {\n    primary: '#60A5FA',\n    primaryHover: '#3B82F6',\n    secondary: '#9CA3AF',\n    background: '#111827',\n    surface: '#1F2937',\n    surfaceHover: '#374151',\n    text: '#F9FAFB',\n    textSecondary: '#D1D5DB',\n    textMuted: '#9CA3AF',\n    border: '#374151',\n    borderHover: '#4B5563',\n    success: '#34D399',\n    warning: '#FBBF24',\n    error: '#F87171',\n    info: '#60A5FA',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowHover: 'rgba(0, 0, 0, 0.4)',\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    weatherCard: '#1F2937',\n    weatherCardHover: '#374151'\n  }\n};\n\n// Theme context\nconst ThemeContext = /*#__PURE__*/createContext();\n\n// Theme provider component\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setIsDarkMode(savedTheme === 'dark');\n    } else {\n      // Check system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setIsDarkMode(prefersDark);\n    }\n  }, []);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = e => {\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        setIsDarkMode(e.matches);\n      }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  // Toggle theme\n  const toggleTheme = () => {\n    const newTheme = !isDarkMode;\n    setIsDarkMode(newTheme);\n    localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n  };\n\n  // Set specific theme\n  const setTheme = theme => {\n    const isDark = theme === 'dark';\n    setIsDarkMode(isDark);\n    localStorage.setItem('theme', theme);\n  };\n\n  // Get current theme object\n  const currentTheme = isDarkMode ? darkTheme : lightTheme;\n  const value = {\n    isDarkMode,\n    theme: currentTheme,\n    toggleTheme,\n    setTheme\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: /*#__PURE__*/_jsxDEV(StyledThemeProvider, {\n      theme: currentTheme,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n\n// Custom hook to use theme\n_s(ThemeProvider, \"jZSDCHM8qUYa7sOOCe+CR2toAGQ=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default ThemeContext;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "ThemeProvider", "StyledThemeProvider", "jsxDEV", "_jsxDEV", "lightTheme", "name", "colors", "primary", "primaryHover", "secondary", "background", "surface", "surfaceHover", "text", "textSecondary", "textMuted", "border", "borderHover", "success", "warning", "error", "info", "shadow", "shadowHover", "gradient", "weatherCard", "weatherCardHover", "spacing", "xs", "sm", "md", "lg", "xl", "xxl", "borderRadius", "full", "fontSize", "base", "fontWeight", "normal", "medium", "semibold", "bold", "breakpoints", "transitions", "fast", "slow", "darkTheme", "ThemeContext", "children", "_s", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "prefersDark", "window", "matchMedia", "matches", "mediaQuery", "handleChange", "e", "addEventListener", "removeEventListener", "toggleTheme", "newTheme", "setItem", "setTheme", "theme", "isDark", "currentTheme", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/contexts/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { ThemeProvider as StyledThemeProvider } from 'styled-components';\n\n// Theme definitions\nexport const lightTheme = {\n  name: 'light',\n  colors: {\n    primary: '#3B82F6',\n    primaryHover: '#2563EB',\n    secondary: '#6B7280',\n    background: '#FFFFFF',\n    surface: '#F9FAFB',\n    surfaceHover: '#F3F4F6',\n    text: '#111827',\n    textSecondary: '#6B7280',\n    textMuted: '#9CA3AF',\n    border: '#E5E7EB',\n    borderHover: '#D1D5DB',\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    info: '#3B82F6',\n    shadow: 'rgba(0, 0, 0, 0.1)',\n    shadowHover: 'rgba(0, 0, 0, 0.15)',\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    weatherCard: '#FFFFFF',\n    weatherCardHover: '#F8FAFC',\n  },\n  spacing: {\n    xs: '0.25rem',\n    sm: '0.5rem',\n    md: '1rem',\n    lg: '1.5rem',\n    xl: '2rem',\n    xxl: '3rem',\n  },\n  borderRadius: {\n    sm: '0.375rem',\n    md: '0.5rem',\n    lg: '0.75rem',\n    xl: '1rem',\n    full: '9999px',\n  },\n  fontSize: {\n    xs: '0.75rem',\n    sm: '0.875rem',\n    base: '1rem',\n    lg: '1.125rem',\n    xl: '1.25rem',\n    '2xl': '1.5rem',\n    '3xl': '1.875rem',\n    '4xl': '2.25rem',\n  },\n  fontWeight: {\n    normal: '400',\n    medium: '500',\n    semibold: '600',\n    bold: '700',\n  },\n  breakpoints: {\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n  },\n  transitions: {\n    fast: '0.15s ease-in-out',\n    normal: '0.3s ease-in-out',\n    slow: '0.5s ease-in-out',\n  },\n};\n\nexport const darkTheme = {\n  ...lightTheme,\n  name: 'dark',\n  colors: {\n    primary: '#60A5FA',\n    primaryHover: '#3B82F6',\n    secondary: '#9CA3AF',\n    background: '#111827',\n    surface: '#1F2937',\n    surfaceHover: '#374151',\n    text: '#F9FAFB',\n    textSecondary: '#D1D5DB',\n    textMuted: '#9CA3AF',\n    border: '#374151',\n    borderHover: '#4B5563',\n    success: '#34D399',\n    warning: '#FBBF24',\n    error: '#F87171',\n    info: '#60A5FA',\n    shadow: 'rgba(0, 0, 0, 0.3)',\n    shadowHover: 'rgba(0, 0, 0, 0.4)',\n    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n    weatherCard: '#1F2937',\n    weatherCardHover: '#374151',\n  },\n};\n\n// Theme context\nconst ThemeContext = createContext();\n\n// Theme provider component\nexport const ThemeProvider = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState(false);\n\n  // Initialize theme from localStorage or system preference\n  useEffect(() => {\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      setIsDarkMode(savedTheme === 'dark');\n    } else {\n      // Check system preference\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      setIsDarkMode(prefersDark);\n    }\n  }, []);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = (e) => {\n      const savedTheme = localStorage.getItem('theme');\n      if (!savedTheme) {\n        setIsDarkMode(e.matches);\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  // Toggle theme\n  const toggleTheme = () => {\n    const newTheme = !isDarkMode;\n    setIsDarkMode(newTheme);\n    localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n  };\n\n  // Set specific theme\n  const setTheme = (theme) => {\n    const isDark = theme === 'dark';\n    setIsDarkMode(isDark);\n    localStorage.setItem('theme', theme);\n  };\n\n  // Get current theme object\n  const currentTheme = isDarkMode ? darkTheme : lightTheme;\n\n  const value = {\n    isDarkMode,\n    theme: currentTheme,\n    toggleTheme,\n    setTheme,\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      <StyledThemeProvider theme={currentTheme}>\n        {children}\n      </StyledThemeProvider>\n    </ThemeContext.Provider>\n  );\n};\n\n// Custom hook to use theme\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport default ThemeContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,mBAAmB;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,OAAO;EACbC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,oBAAoB;IAC5BC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE,mDAAmD;IAC7DC,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE;EACpB,CAAC;EACDC,OAAO,EAAE;IACPC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE;EACP,CAAC;EACDC,YAAY,EAAE;IACZL,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,MAAM;IACVG,IAAI,EAAE;EACR,CAAC;EACDC,QAAQ,EAAE;IACRR,EAAE,EAAE,SAAS;IACbC,EAAE,EAAE,UAAU;IACdQ,IAAI,EAAE,MAAM;IACZN,EAAE,EAAE,UAAU;IACdC,EAAE,EAAE,SAAS;IACb,KAAK,EAAE,QAAQ;IACf,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE;EACT,CAAC;EACDM,UAAU,EAAE;IACVC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;EACR,CAAC;EACDC,WAAW,EAAE;IACXd,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE;EACN,CAAC;EACDY,WAAW,EAAE;IACXC,IAAI,EAAE,mBAAmB;IACzBN,MAAM,EAAE,kBAAkB;IAC1BO,IAAI,EAAE;EACR;AACF,CAAC;AAED,OAAO,MAAMC,SAAS,GAAG;EACvB,GAAG3C,UAAU;EACbC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,SAAS,EAAE,SAAS;IACpBC,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE,SAAS;IACtBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,oBAAoB;IAC5BC,WAAW,EAAE,oBAAoB;IACjCC,QAAQ,EAAE,mDAAmD;IAC7DC,WAAW,EAAE,SAAS;IACtBC,gBAAgB,EAAE;EACpB;AACF,CAAC;;AAED;AACA,MAAMsB,YAAY,gBAAGpD,aAAa,CAAC,CAAC;;AAEpC;AACA,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEiD;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMsD,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACdD,aAAa,CAACC,UAAU,KAAK,MAAM,CAAC;IACtC,CAAC,MAAM;MACL;MACA,MAAMG,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAC7EP,aAAa,CAACI,WAAW,CAAC;IAC5B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAzD,SAAS,CAAC,MAAM;IACd,MAAM6D,UAAU,GAAGH,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IACpE,MAAMG,YAAY,GAAIC,CAAC,IAAK;MAC1B,MAAMT,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAChD,IAAI,CAACF,UAAU,EAAE;QACfD,aAAa,CAACU,CAAC,CAACH,OAAO,CAAC;MAC1B;IACF,CAAC;IAEDC,UAAU,CAACG,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACnD,OAAO,MAAMD,UAAU,CAACI,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAG,CAACf,UAAU;IAC5BC,aAAa,CAACc,QAAQ,CAAC;IACvBZ,YAAY,CAACa,OAAO,CAAC,OAAO,EAAED,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;EAC5D,CAAC;;EAED;EACA,MAAME,QAAQ,GAAIC,KAAK,IAAK;IAC1B,MAAMC,MAAM,GAAGD,KAAK,KAAK,MAAM;IAC/BjB,aAAa,CAACkB,MAAM,CAAC;IACrBhB,YAAY,CAACa,OAAO,CAAC,OAAO,EAAEE,KAAK,CAAC;EACtC,CAAC;;EAED;EACA,MAAME,YAAY,GAAGpB,UAAU,GAAGJ,SAAS,GAAG3C,UAAU;EAExD,MAAMoE,KAAK,GAAG;IACZrB,UAAU;IACVkB,KAAK,EAAEE,YAAY;IACnBN,WAAW;IACXG;EACF,CAAC;EAED,oBACEjE,OAAA,CAAC6C,YAAY,CAACyB,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,eAClC9C,OAAA,CAACF,mBAAmB;MAACoE,KAAK,EAAEE,YAAa;MAAAtB,QAAA,EACtCA;IAAQ;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE5B,CAAC;;AAED;AAAA3B,EAAA,CA9DalD,aAAa;AAAA8E,EAAA,GAAb9E,aAAa;AA+D1B,OAAO,MAAM+E,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGpF,UAAU,CAACmD,YAAY,CAAC;EACxC,IAAI,CAACiC,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAQrB,eAAe/B,YAAY;AAAC,IAAA8B,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}