import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.text};
    transition: background-color ${({ theme }) => theme.transitions.normal},
                color ${({ theme }) => theme.transitions.normal};
    line-height: 1.6;
    min-height: 100vh;
  }

  #root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colors.surface};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.colors.border};
    border-radius: ${({ theme }) => theme.borderRadius.full};
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.colors.borderHover};
  }

  /* Focus styles */
  *:focus {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  /* Button reset */
  button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
  }

  /* Input reset */
  input, textarea, select {
    font-family: inherit;
    font-size: inherit;
  }

  /* Link styles */
  a {
    color: ${({ theme }) => theme.colors.primary};
    text-decoration: none;
    transition: color ${({ theme }) => theme.transitions.fast};
  }

  a:hover {
    color: ${({ theme }) => theme.colors.primaryHover};
  }

  /* Utility classes */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 ${({ theme }) => theme.spacing.md};
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .text-right {
    text-align: right;
  }

  .flex {
    display: flex;
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .gap-1 {
    gap: ${({ theme }) => theme.spacing.xs};
  }

  .gap-2 {
    gap: ${({ theme }) => theme.spacing.sm};
  }

  .gap-4 {
    gap: ${({ theme }) => theme.spacing.md};
  }

  .gap-6 {
    gap: ${({ theme }) => theme.spacing.lg};
  }

  .mb-2 {
    margin-bottom: ${({ theme }) => theme.spacing.sm};
  }

  .mb-4 {
    margin-bottom: ${({ theme }) => theme.spacing.md};
  }

  .mb-6 {
    margin-bottom: ${({ theme }) => theme.spacing.lg};
  }

  .mt-2 {
    margin-top: ${({ theme }) => theme.spacing.sm};
  }

  .mt-4 {
    margin-top: ${({ theme }) => theme.spacing.md};
  }

  .mt-6 {
    margin-top: ${({ theme }) => theme.spacing.lg};
  }

  .p-2 {
    padding: ${({ theme }) => theme.spacing.sm};
  }

  .p-4 {
    padding: ${({ theme }) => theme.spacing.md};
  }

  .p-6 {
    padding: ${({ theme }) => theme.spacing.lg};
  }

  .rounded {
    border-radius: ${({ theme }) => theme.borderRadius.md};
  }

  .rounded-lg {
    border-radius: ${({ theme }) => theme.borderRadius.lg};
  }

  .shadow {
    box-shadow: 0 1px 3px 0 ${({ theme }) => theme.colors.shadow};
  }

  .shadow-lg {
    box-shadow: 0 10px 15px -3px ${({ theme }) => theme.colors.shadow};
  }

  /* Animation classes */
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes scaleIn {
    from {
      transform: scale(0.95);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Loading spinner */
  .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid ${({ theme }) => theme.colors.border};
    border-top: 2px solid ${({ theme }) => theme.colors.primary};
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive utilities */
  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {
    .container {
      padding: 0 ${({ theme }) => theme.spacing.sm};
    }
    
    .hidden-mobile {
      display: none;
    }
  }

  @media (min-width: ${({ theme }) => theme.breakpoints.sm}) {
    .hidden-desktop {
      display: none;
    }
  }
`;

export default GlobalStyles;
