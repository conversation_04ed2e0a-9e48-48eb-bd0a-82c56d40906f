{"ast": null, "code": "var _jsxFileName = \"D:\\\\weather-app\\\\frontend\\\\src\\\\components\\\\layout\\\\Layout.js\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LayoutContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: ${({\n  theme\n}) => theme.colors.background};\n`;\n_c = LayoutContainer;\nconst Main = styled.main`\n  flex: 1;\n  padding: ${({\n  theme\n}) => theme.spacing.lg} 0;\n  \n  @media (max-width: ${({\n  theme\n}) => theme.breakpoints.sm}) {\n    padding: ${({\n  theme\n}) => theme.spacing.md} 0;\n  }\n`;\n_c2 = Main;\nconst Layout = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(LayoutContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Main, {\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n_c3 = Layout;\nexport default Layout;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"LayoutContainer\");\n$RefreshReg$(_c2, \"Main\");\n$RefreshReg$(_c3, \"Layout\");", "map": {"version": 3, "names": ["React", "styled", "Header", "Footer", "jsxDEV", "_jsxDEV", "LayoutContainer", "div", "theme", "colors", "background", "_c", "Main", "main", "spacing", "lg", "breakpoints", "sm", "md", "_c2", "Layout", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c3", "$RefreshReg$"], "sources": ["D:/weather-app/frontend/src/components/layout/Layout.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport Header from './Header';\nimport Footer from './Footer';\n\nconst LayoutContainer = styled.div`\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: ${({ theme }) => theme.colors.background};\n`;\n\nconst Main = styled.main`\n  flex: 1;\n  padding: ${({ theme }) => theme.spacing.lg} 0;\n  \n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    padding: ${({ theme }) => theme.spacing.md} 0;\n  }\n`;\n\nconst Layout = ({ children }) => {\n  return (\n    <LayoutContainer>\n      <Header />\n      <Main>{children}</Main>\n      <Footer />\n    </LayoutContainer>\n  );\n};\n\nexport default Layout;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,eAAe,GAAGL,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA,sBAAsB,CAAC;EAAEC;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,UAAU;AAC5D,CAAC;AAACC,EAAA,GALIL,eAAe;AAOrB,MAAMM,IAAI,GAAGX,MAAM,CAACY,IAAI;AACxB;AACA,aAAa,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACC,EAAE;AAC5C;AACA,uBAAuB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACQ,WAAW,CAACC,EAAE;AAC1D,eAAe,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACM,OAAO,CAACI,EAAE;AAC9C;AACA,CAAC;AAACC,GAAA,GAPIP,IAAI;AASV,MAAMQ,MAAM,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC/B,oBACEhB,OAAA,CAACC,eAAe;IAAAe,QAAA,gBACdhB,OAAA,CAACH,MAAM;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVpB,OAAA,CAACO,IAAI;MAAAS,QAAA,EAAEA;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACvBpB,OAAA,CAACF,MAAM;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEtB,CAAC;AAACC,GAAA,GARIN,MAAM;AAUZ,eAAeA,MAAM;AAAC,IAAAT,EAAA,EAAAQ,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAhB,EAAA;AAAAgB,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}