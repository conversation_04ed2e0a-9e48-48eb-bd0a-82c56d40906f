const mongoose = require('mongoose');

const FavoriteSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  city: {
    type: String,
    required: [true, 'City name is required'],
    trim: true
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true
  },
  coordinates: {
    lat: {
      type: Number,
      required: true
    },
    lon: {
      type: Number,
      required: true
    }
  },
  displayName: {
    type: String,
    required: true,
    trim: true
  },
  timezone: {
    type: String,
    default: null
  },
  isDefault: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true
});

// Compound index to ensure unique city per user
FavoriteSchema.index({ user: 1, city: 1, country: 1 }, { unique: true });

// Index for user lookup
FavoriteSchema.index({ user: 1 });

// Static method to get user's favorites
FavoriteSchema.statics.getUserFavorites = function(userId) {
  return this.find({ user: userId }).sort({ isDefault: -1, createdAt: -1 });
};

// Static method to check if city is already favorited by user
FavoriteSchema.statics.isFavorited = function(userId, city, country) {
  return this.findOne({ 
    user: userId, 
    city: city.toLowerCase(), 
    country: country.toLowerCase() 
  });
};

// Pre-save middleware to normalize city and country names
FavoriteSchema.pre('save', function(next) {
  this.city = this.city.toLowerCase();
  this.country = this.country.toLowerCase();
  next();
});

// Instance method to format for API response
FavoriteSchema.methods.toAPIResponse = function() {
  return {
    id: this._id,
    city: this.city,
    country: this.country,
    displayName: this.displayName,
    coordinates: this.coordinates,
    timezone: this.timezone,
    isDefault: this.isDefault,
    createdAt: this.createdAt,
    updatedAt: this.updatedAt
  };
};

module.exports = mongoose.model('Favorite', FavoriteSchema);
