import React from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import Card from '../common/Card';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
`;

const WelcomeSection = styled.section`
  margin-bottom: ${({ theme }) => theme.spacing.xl};
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Subtitle = styled.p`
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSize.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const ComingSoonCard = styled(Card)`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.xxl};
`;

const ComingSoonIcon = styled.div`
  font-size: 4rem;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <DashboardContainer>
      <WelcomeSection>
        <Title>Welcome back, {user?.name}!</Title>
        <Subtitle>
          Your personalized weather dashboard is coming soon.
        </Subtitle>
      </WelcomeSection>

      <ComingSoonCard>
        <Card.Content>
          <ComingSoonIcon>🚧</ComingSoonIcon>
          <Card.Title>Dashboard Coming Soon</Card.Title>
          <Card.Subtitle>
            We're working hard to bring you an amazing dashboard experience with:
          </Card.Subtitle>
          <div style={{ marginTop: '2rem', textAlign: 'left', maxWidth: '400px', margin: '2rem auto 0' }}>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li style={{ marginBottom: '1rem' }}>⭐ Your favorite cities</li>
              <li style={{ marginBottom: '1rem' }}>📊 Weather analytics</li>
              <li style={{ marginBottom: '1rem' }}>🔔 Weather alerts</li>
              <li style={{ marginBottom: '1rem' }}>📱 Personalized widgets</li>
              <li style={{ marginBottom: '1rem' }}>📈 Historical weather data</li>
            </ul>
          </div>
          <p style={{ marginTop: '2rem', color: 'var(--text-secondary)' }}>
            In the meantime, you can search for weather information on the home page.
          </p>
        </Card.Content>
      </ComingSoonCard>
    </DashboardContainer>
  );
};

export default Dashboard;
