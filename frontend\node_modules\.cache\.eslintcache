[{"D:\\weather-app\\frontend\\src\\index.js": "1", "D:\\weather-app\\frontend\\src\\App.js": "2", "D:\\weather-app\\frontend\\src\\reportWebVitals.js": "3", "D:\\weather-app\\frontend\\src\\styles\\GlobalStyles.js": "4", "D:\\weather-app\\frontend\\src\\contexts\\AuthContext.js": "5", "D:\\weather-app\\frontend\\src\\contexts\\ThemeContext.js": "6", "D:\\weather-app\\frontend\\src\\components\\pages\\Home.js": "7", "D:\\weather-app\\frontend\\src\\components\\pages\\Login.js": "8", "D:\\weather-app\\frontend\\src\\components\\pages\\Dashboard.js": "9", "D:\\weather-app\\frontend\\src\\components\\layout\\Layout.js": "10", "D:\\weather-app\\frontend\\src\\components\\pages\\Register.js": "11", "D:\\weather-app\\frontend\\src\\components\\auth\\ProtectedRoute.js": "12", "D:\\weather-app\\frontend\\src\\components\\pages\\Profile.js": "13", "D:\\weather-app\\frontend\\src\\components\\layout\\Header.js": "14", "D:\\weather-app\\frontend\\src\\services\\api.js": "15", "D:\\weather-app\\frontend\\src\\components\\layout\\Footer.js": "16", "D:\\weather-app\\frontend\\src\\components\\common\\Card.js": "17", "D:\\weather-app\\frontend\\src\\components\\weather\\WeatherCard.js": "18", "D:\\weather-app\\frontend\\src\\components\\common\\Button.js": "19", "D:\\weather-app\\frontend\\src\\components\\common\\Input.js": "20", "D:\\weather-app\\frontend\\src\\components\\common\\LoadingSpinner.js": "21", "D:\\weather-app\\frontend\\src\\components\\weather\\WeatherSearch.js": "22", "D:\\weather-app\\frontend\\src\\components\\weather\\ForecastCard.js": "23"}, {"size": 513, "mtime": 1752232234238, "results": "24", "hashOfConfig": "25"}, {"size": 1598, "mtime": 1752231960993, "results": "26", "hashOfConfig": "25"}, {"size": 362, "mtime": 1752230464823, "results": "27", "hashOfConfig": "25"}, {"size": 5146, "mtime": 1752231887916, "results": "28", "hashOfConfig": "25"}, {"size": 5468, "mtime": 1752231836514, "results": "29", "hashOfConfig": "25"}, {"size": 4183, "mtime": 1752231863110, "results": "30", "hashOfConfig": "25"}, {"size": 6394, "mtime": 1752232424283, "results": "31", "hashOfConfig": "25"}, {"size": 5626, "mtime": 1752232153027, "results": "32", "hashOfConfig": "25"}, {"size": 2392, "mtime": 1752232198418, "results": "33", "hashOfConfig": "25"}, {"size": 695, "mtime": 1752231980612, "results": "34", "hashOfConfig": "25"}, {"size": 7920, "mtime": 1752232181988, "results": "35", "hashOfConfig": "25"}, {"size": 583, "mtime": 1752232035034, "results": "36", "hashOfConfig": "25"}, {"size": 7111, "mtime": 1752232225628, "results": "37", "hashOfConfig": "25"}, {"size": 6888, "mtime": 1752232006078, "results": "38", "hashOfConfig": "25"}, {"size": 2665, "mtime": 1752231813613, "results": "39", "hashOfConfig": "25"}, {"size": 2004, "mtime": 1752232018233, "results": "40", "hashOfConfig": "25"}, {"size": 3513, "mtime": 1752231948818, "results": "41", "hashOfConfig": "25"}, {"size": 7212, "mtime": 1752232122910, "results": "42", "hashOfConfig": "25"}, {"size": 3845, "mtime": 1752231907812, "results": "43", "hashOfConfig": "25"}, {"size": 4861, "mtime": 1752231929808, "results": "44", "hashOfConfig": "25"}, {"size": 1430, "mtime": 1752232046248, "results": "45", "hashOfConfig": "25"}, {"size": 6490, "mtime": 1752232355786, "results": "46", "hashOfConfig": "25"}, {"size": 6120, "mtime": 1752232384287, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ci96gf", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\weather-app\\frontend\\src\\index.js", [], [], "D:\\weather-app\\frontend\\src\\App.js", [], [], "D:\\weather-app\\frontend\\src\\reportWebVitals.js", [], [], "D:\\weather-app\\frontend\\src\\styles\\GlobalStyles.js", [], [], "D:\\weather-app\\frontend\\src\\contexts\\AuthContext.js", [], [], "D:\\weather-app\\frontend\\src\\contexts\\ThemeContext.js", [], [], "D:\\weather-app\\frontend\\src\\components\\pages\\Home.js", ["117", "118", "119", "120", "121", "122", "123"], [], "D:\\weather-app\\frontend\\src\\components\\pages\\Login.js", [], [], "D:\\weather-app\\frontend\\src\\components\\pages\\Dashboard.js", [], [], "D:\\weather-app\\frontend\\src\\components\\layout\\Layout.js", [], [], "D:\\weather-app\\frontend\\src\\components\\pages\\Register.js", [], [], "D:\\weather-app\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "D:\\weather-app\\frontend\\src\\components\\pages\\Profile.js", [], [], "D:\\weather-app\\frontend\\src\\components\\layout\\Header.js", [], [], "D:\\weather-app\\frontend\\src\\services\\api.js", [], [], "D:\\weather-app\\frontend\\src\\components\\layout\\Footer.js", [], [], "D:\\weather-app\\frontend\\src\\components\\common\\Card.js", [], [], "D:\\weather-app\\frontend\\src\\components\\weather\\WeatherCard.js", [], [], "D:\\weather-app\\frontend\\src\\components\\common\\Button.js", [], [], "D:\\weather-app\\frontend\\src\\components\\common\\Input.js", [], [], "D:\\weather-app\\frontend\\src\\components\\common\\LoadingSpinner.js", [], [], "D:\\weather-app\\frontend\\src\\components\\weather\\WeatherSearch.js", [], [], "D:\\weather-app\\frontend\\src\\components\\weather\\ForecastCard.js", [], [], {"ruleId": "124", "severity": 1, "message": "125", "line": 11, "column": 8, "nodeType": "126", "messageId": "127", "endLine": 11, "endColumn": 20}, {"ruleId": "124", "severity": 1, "message": "128", "line": 53, "column": 7, "nodeType": "126", "messageId": "127", "endLine": 53, "endColumn": 17}, {"ruleId": "124", "severity": 1, "message": "129", "line": 63, "column": 7, "nodeType": "126", "messageId": "127", "endLine": 63, "endColumn": 18}, {"ruleId": "130", "severity": 2, "message": "131", "line": 63, "column": 28, "nodeType": "126", "messageId": "132", "endLine": 63, "endColumn": 33}, {"ruleId": "124", "severity": 1, "message": "133", "line": 97, "column": 10, "nodeType": "126", "messageId": "127", "endLine": 97, "endColumn": 22}, {"ruleId": "124", "severity": 1, "message": "134", "line": 100, "column": 10, "nodeType": "126", "messageId": "127", "endLine": 100, "endColumn": 22}, {"ruleId": "124", "severity": 1, "message": "135", "line": 121, "column": 9, "nodeType": "126", "messageId": "127", "endLine": 121, "endColumn": 27}, "no-unused-vars", "'ForecastCard' is defined but never used.", "Identifier", "unusedVar", "'SearchForm' is assigned a value but never used.", "'SearchInput' is assigned a value but never used.", "no-undef", "'Input' is not defined.", "undef", "'selectedCity' is assigned a value but never used.", "'showForecast' is assigned a value but never used.", "'handleViewForecast' is assigned a value but never used."]