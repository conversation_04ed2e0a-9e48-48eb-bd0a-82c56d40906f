import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import Button from '../common/Button';
import Input from '../common/Input';
import Card from '../common/Card';

const ProfileContainer = styled.div`
  max-width: 600px;
  margin: 0 auto;
  padding: 0 ${({ theme }) => theme.spacing.md};
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.text};
  margin-bottom: ${({ theme }) => theme.spacing.xl};
  text-align: center;
`;

const ProfileCard = styled(Card)`
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

const ThemeSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing.md};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  background-color: ${({ theme }) => theme.colors.surface};
`;

const ThemeInfo = styled.div`
  h3 {
    margin: 0 0 ${({ theme }) => theme.spacing.xs} 0;
    color: ${({ theme }) => theme.colors.text};
  }
  
  p {
    margin: 0;
    color: ${({ theme }) => theme.colors.textSecondary};
    font-size: ${({ theme }) => theme.fontSize.sm};
  }
`;

const SuccessMessage = styled.div`
  background-color: ${({ theme }) => theme.colors.success}20;
  border: 1px solid ${({ theme }) => theme.colors.success};
  color: ${({ theme }) => theme.colors.success};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSize.sm};
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const ErrorMessage = styled.div`
  background-color: ${({ theme }) => theme.colors.error}20;
  border: 1px solid ${({ theme }) => theme.colors.error};
  color: ${({ theme }) => theme.colors.error};
  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  font-size: ${({ theme }) => theme.fontSize.sm};
  text-align: center;
  margin-bottom: ${({ theme }) => theme.spacing.md};
`;

const Profile = () => {
  const { user, updateProfile, isLoading, error, clearError } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
  });
  const [successMessage, setSuccessMessage] = useState('');
  const [validationErrors, setValidationErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Clear messages
    setSuccessMessage('');
    clearError();
  };

  const validateForm = () => {
    const errors = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters long';
    }
    
    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    clearError();
    setSuccessMessage('');
    
    const result = await updateProfile({
      name: formData.name.trim(),
      // Note: Email updates would typically require email verification
      // For now, we'll only update the name
    });
    
    if (result.success) {
      setSuccessMessage('Profile updated successfully!');
    }
  };

  return (
    <ProfileContainer>
      <Title>Profile Settings</Title>
      
      <ProfileCard>
        <Card.Header>
          <Card.Title>Personal Information</Card.Title>
        </Card.Header>
        <Card.Content>
          {successMessage && (
            <SuccessMessage>{successMessage}</SuccessMessage>
          )}
          
          {error && (
            <ErrorMessage>{error}</ErrorMessage>
          )}
          
          <Form onSubmit={handleSubmit}>
            <Input
              type="text"
              name="name"
              label="Full Name"
              placeholder="Enter your full name"
              value={formData.name}
              onChange={handleChange}
              error={validationErrors.name}
              required
              disabled={isLoading}
            />
            
            <Input
              type="email"
              name="email"
              label="Email Address"
              placeholder="Enter your email"
              value={formData.email}
              onChange={handleChange}
              error={validationErrors.email}
              required
              disabled={true} // Email updates disabled for now
              helperText="Email updates are currently disabled"
            />
            
            <Button
              type="submit"
              loading={isLoading}
              disabled={isLoading || formData.name === user?.name}
            >
              {isLoading ? 'Updating...' : 'Update Profile'}
            </Button>
          </Form>
        </Card.Content>
      </ProfileCard>

      <ProfileCard>
        <Card.Header>
          <Card.Title>Appearance</Card.Title>
        </Card.Header>
        <Card.Content>
          <ThemeSection>
            <ThemeInfo>
              <h3>Theme</h3>
              <p>Choose your preferred color scheme</p>
            </ThemeInfo>
            <Button
              variant="outline"
              onClick={toggleTheme}
            >
              {isDarkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
            </Button>
          </ThemeSection>
        </Card.Content>
      </ProfileCard>

      <ProfileCard>
        <Card.Header>
          <Card.Title>Account Information</Card.Title>
        </Card.Header>
        <Card.Content>
          <div style={{ display: 'grid', gap: '1rem' }}>
            <div>
              <strong>Member since:</strong>{' '}
              {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'Unknown'}
            </div>
            <div>
              <strong>Last login:</strong>{' '}
              {user?.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Unknown'}
            </div>
            <div>
              <strong>Account status:</strong>{' '}
              <span style={{ color: '#10B981' }}>Active</span>
            </div>
          </div>
        </Card.Content>
      </ProfileCard>
    </ProfileContainer>
  );
};

export default Profile;
