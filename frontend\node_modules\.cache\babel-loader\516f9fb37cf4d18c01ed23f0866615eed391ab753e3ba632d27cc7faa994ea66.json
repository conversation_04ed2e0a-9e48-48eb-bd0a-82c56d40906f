{"ast": null, "code": "import { createGlobalStyle } from 'styled-components';\nconst GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html {\n    font-size: 16px;\n    scroll-behavior: smooth;\n  }\n\n  body {\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: ${({\n  theme\n}) => theme.colors.background};\n    color: ${({\n  theme\n}) => theme.colors.text};\n    transition: background-color ${({\n  theme\n}) => theme.transitions.normal},\n                color ${({\n  theme\n}) => theme.transitions.normal};\n    line-height: 1.6;\n    min-height: 100vh;\n  }\n\n  #root {\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n  }\n\n  /* Scrollbar styles */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${({\n  theme\n}) => theme.colors.surface};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${({\n  theme\n}) => theme.colors.border};\n    border-radius: ${({\n  theme\n}) => theme.borderRadius.full};\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${({\n  theme\n}) => theme.colors.borderHover};\n  }\n\n  /* Focus styles */\n  *:focus {\n    outline: 2px solid ${({\n  theme\n}) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Button reset */\n  button {\n    border: none;\n    background: none;\n    cursor: pointer;\n    font-family: inherit;\n  }\n\n  /* Input reset */\n  input, textarea, select {\n    font-family: inherit;\n    font-size: inherit;\n  }\n\n  /* Link styles */\n  a {\n    color: ${({\n  theme\n}) => theme.colors.primary};\n    text-decoration: none;\n    transition: color ${({\n  theme\n}) => theme.transitions.fast};\n  }\n\n  a:hover {\n    color: ${({\n  theme\n}) => theme.colors.primaryHover};\n  }\n\n  /* Utility classes */\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 ${({\n  theme\n}) => theme.spacing.md};\n  }\n\n  .text-center {\n    text-align: center;\n  }\n\n  .text-left {\n    text-align: left;\n  }\n\n  .text-right {\n    text-align: right;\n  }\n\n  .flex {\n    display: flex;\n  }\n\n  .flex-col {\n    flex-direction: column;\n  }\n\n  .items-center {\n    align-items: center;\n  }\n\n  .justify-center {\n    justify-content: center;\n  }\n\n  .justify-between {\n    justify-content: space-between;\n  }\n\n  .gap-1 {\n    gap: ${({\n  theme\n}) => theme.spacing.xs};\n  }\n\n  .gap-2 {\n    gap: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n\n  .gap-4 {\n    gap: ${({\n  theme\n}) => theme.spacing.md};\n  }\n\n  .gap-6 {\n    gap: ${({\n  theme\n}) => theme.spacing.lg};\n  }\n\n  .mb-2 {\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n\n  .mb-4 {\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.md};\n  }\n\n  .mb-6 {\n    margin-bottom: ${({\n  theme\n}) => theme.spacing.lg};\n  }\n\n  .mt-2 {\n    margin-top: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n\n  .mt-4 {\n    margin-top: ${({\n  theme\n}) => theme.spacing.md};\n  }\n\n  .mt-6 {\n    margin-top: ${({\n  theme\n}) => theme.spacing.lg};\n  }\n\n  .p-2 {\n    padding: ${({\n  theme\n}) => theme.spacing.sm};\n  }\n\n  .p-4 {\n    padding: ${({\n  theme\n}) => theme.spacing.md};\n  }\n\n  .p-6 {\n    padding: ${({\n  theme\n}) => theme.spacing.lg};\n  }\n\n  .rounded {\n    border-radius: ${({\n  theme\n}) => theme.borderRadius.md};\n  }\n\n  .rounded-lg {\n    border-radius: ${({\n  theme\n}) => theme.borderRadius.lg};\n  }\n\n  .shadow {\n    box-shadow: 0 1px 3px 0 ${({\n  theme\n}) => theme.colors.shadow};\n  }\n\n  .shadow-lg {\n    box-shadow: 0 10px 15px -3px ${({\n  theme\n}) => theme.colors.shadow};\n  }\n\n  /* Animation classes */\n  .fade-in {\n    animation: fadeIn 0.3s ease-in-out;\n  }\n\n  .slide-up {\n    animation: slideUp 0.3s ease-out;\n  }\n\n  .scale-in {\n    animation: scaleIn 0.2s ease-out;\n  }\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  @keyframes slideUp {\n    from {\n      transform: translateY(20px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes scaleIn {\n    from {\n      transform: scale(0.95);\n      opacity: 0;\n    }\n    to {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n\n  /* Loading spinner */\n  .spinner {\n    width: 20px;\n    height: 20px;\n    border: 2px solid ${({\n  theme\n}) => theme.colors.border};\n    border-top: 2px solid ${({\n  theme\n}) => theme.colors.primary};\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n\n  /* Responsive utilities */\n  @media (max-width: ${({\n  theme\n}) => theme.breakpoints.sm}) {\n    .container {\n      padding: 0 ${({\n  theme\n}) => theme.spacing.sm};\n    }\n    \n    .hidden-mobile {\n      display: none;\n    }\n  }\n\n  @media (min-width: ${({\n  theme\n}) => theme.breakpoints.sm}) {\n    .hidden-desktop {\n      display: none;\n    }\n  }\n`;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["createGlobalStyle", "GlobalStyles", "theme", "colors", "background", "text", "transitions", "normal", "surface", "border", "borderRadius", "full", "borderHover", "primary", "fast", "primaryHover", "spacing", "md", "xs", "sm", "lg", "shadow", "breakpoints"], "sources": ["D:/weather-app/frontend/src/styles/GlobalStyles.js"], "sourcesContent": ["import { createGlobalStyle } from 'styled-components';\n\nconst GlobalStyles = createGlobalStyle`\n  * {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n  }\n\n  html {\n    font-size: 16px;\n    scroll-behavior: smooth;\n  }\n\n  body {\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n      sans-serif;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    background-color: ${({ theme }) => theme.colors.background};\n    color: ${({ theme }) => theme.colors.text};\n    transition: background-color ${({ theme }) => theme.transitions.normal},\n                color ${({ theme }) => theme.transitions.normal};\n    line-height: 1.6;\n    min-height: 100vh;\n  }\n\n  #root {\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n  }\n\n  /* Scrollbar styles */\n  ::-webkit-scrollbar {\n    width: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${({ theme }) => theme.colors.surface};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${({ theme }) => theme.colors.border};\n    border-radius: ${({ theme }) => theme.borderRadius.full};\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${({ theme }) => theme.colors.borderHover};\n  }\n\n  /* Focus styles */\n  *:focus {\n    outline: 2px solid ${({ theme }) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Button reset */\n  button {\n    border: none;\n    background: none;\n    cursor: pointer;\n    font-family: inherit;\n  }\n\n  /* Input reset */\n  input, textarea, select {\n    font-family: inherit;\n    font-size: inherit;\n  }\n\n  /* Link styles */\n  a {\n    color: ${({ theme }) => theme.colors.primary};\n    text-decoration: none;\n    transition: color ${({ theme }) => theme.transitions.fast};\n  }\n\n  a:hover {\n    color: ${({ theme }) => theme.colors.primaryHover};\n  }\n\n  /* Utility classes */\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border: 0;\n  }\n\n  .container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 0 ${({ theme }) => theme.spacing.md};\n  }\n\n  .text-center {\n    text-align: center;\n  }\n\n  .text-left {\n    text-align: left;\n  }\n\n  .text-right {\n    text-align: right;\n  }\n\n  .flex {\n    display: flex;\n  }\n\n  .flex-col {\n    flex-direction: column;\n  }\n\n  .items-center {\n    align-items: center;\n  }\n\n  .justify-center {\n    justify-content: center;\n  }\n\n  .justify-between {\n    justify-content: space-between;\n  }\n\n  .gap-1 {\n    gap: ${({ theme }) => theme.spacing.xs};\n  }\n\n  .gap-2 {\n    gap: ${({ theme }) => theme.spacing.sm};\n  }\n\n  .gap-4 {\n    gap: ${({ theme }) => theme.spacing.md};\n  }\n\n  .gap-6 {\n    gap: ${({ theme }) => theme.spacing.lg};\n  }\n\n  .mb-2 {\n    margin-bottom: ${({ theme }) => theme.spacing.sm};\n  }\n\n  .mb-4 {\n    margin-bottom: ${({ theme }) => theme.spacing.md};\n  }\n\n  .mb-6 {\n    margin-bottom: ${({ theme }) => theme.spacing.lg};\n  }\n\n  .mt-2 {\n    margin-top: ${({ theme }) => theme.spacing.sm};\n  }\n\n  .mt-4 {\n    margin-top: ${({ theme }) => theme.spacing.md};\n  }\n\n  .mt-6 {\n    margin-top: ${({ theme }) => theme.spacing.lg};\n  }\n\n  .p-2 {\n    padding: ${({ theme }) => theme.spacing.sm};\n  }\n\n  .p-4 {\n    padding: ${({ theme }) => theme.spacing.md};\n  }\n\n  .p-6 {\n    padding: ${({ theme }) => theme.spacing.lg};\n  }\n\n  .rounded {\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n  }\n\n  .rounded-lg {\n    border-radius: ${({ theme }) => theme.borderRadius.lg};\n  }\n\n  .shadow {\n    box-shadow: 0 1px 3px 0 ${({ theme }) => theme.colors.shadow};\n  }\n\n  .shadow-lg {\n    box-shadow: 0 10px 15px -3px ${({ theme }) => theme.colors.shadow};\n  }\n\n  /* Animation classes */\n  .fade-in {\n    animation: fadeIn 0.3s ease-in-out;\n  }\n\n  .slide-up {\n    animation: slideUp 0.3s ease-out;\n  }\n\n  .scale-in {\n    animation: scaleIn 0.2s ease-out;\n  }\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n\n  @keyframes slideUp {\n    from {\n      transform: translateY(20px);\n      opacity: 0;\n    }\n    to {\n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n\n  @keyframes scaleIn {\n    from {\n      transform: scale(0.95);\n      opacity: 0;\n    }\n    to {\n      transform: scale(1);\n      opacity: 1;\n    }\n  }\n\n  /* Loading spinner */\n  .spinner {\n    width: 20px;\n    height: 20px;\n    border: 2px solid ${({ theme }) => theme.colors.border};\n    border-top: 2px solid ${({ theme }) => theme.colors.primary};\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n\n  /* Responsive utilities */\n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    .container {\n      padding: 0 ${({ theme }) => theme.spacing.sm};\n    }\n    \n    .hidden-mobile {\n      display: none;\n    }\n  }\n\n  @media (min-width: ${({ theme }) => theme.breakpoints.sm}) {\n    .hidden-desktop {\n      display: none;\n    }\n  }\n`;\n\nexport default GlobalStyles;\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mBAAmB;AAErD,MAAMC,YAAY,GAAGD,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,CAAC;EAAEE;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACC,UAAU;AAC9D,aAAa,CAAC;EAAEF;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACE,IAAI;AAC7C,mCAAmC,CAAC;EAAEH;AAAM,CAAC,KAAKA,KAAK,CAACI,WAAW,CAACC,MAAM;AAC1E,wBAAwB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACI,WAAW,CAACC,MAAM;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,CAAC;EAAEL;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACK,OAAO;AACrD;AACA;AACA;AACA,kBAAkB,CAAC;EAAEN;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,MAAM;AACpD,qBAAqB,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACQ,YAAY,CAACC,IAAI;AAC3D;AACA;AACA;AACA,kBAAkB,CAAC;EAAET;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACS,WAAW;AACzD;AACA;AACA;AACA;AACA,yBAAyB,CAAC;EAAEV;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACU,OAAO;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACU,OAAO;AAChD;AACA,wBAAwB,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACI,WAAW,CAACQ,IAAI;AAC7D;AACA;AACA;AACA,aAAa,CAAC;EAAEZ;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACY,YAAY;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,CAAC;EAAEb;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACC,EAAE;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACE,EAAE;AAC1C;AACA;AACA;AACA,WAAW,CAAC;EAAEhB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACG,EAAE;AAC1C;AACA;AACA;AACA,WAAW,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACC,EAAE;AAC1C;AACA;AACA;AACA,WAAW,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACI,EAAE;AAC1C;AACA;AACA;AACA,qBAAqB,CAAC;EAAElB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACG,EAAE;AACpD;AACA;AACA;AACA,qBAAqB,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACC,EAAE;AACpD;AACA;AACA;AACA,qBAAqB,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACI,EAAE;AACpD;AACA;AACA;AACA,kBAAkB,CAAC;EAAElB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACG,EAAE;AACjD;AACA;AACA;AACA,kBAAkB,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACC,EAAE;AACjD;AACA;AACA;AACA,kBAAkB,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACI,EAAE;AACjD;AACA;AACA;AACA,eAAe,CAAC;EAAElB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACG,EAAE;AAC9C;AACA;AACA;AACA,eAAe,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACC,EAAE;AAC9C;AACA;AACA;AACA,eAAe,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACI,EAAE;AAC9C;AACA;AACA;AACA,qBAAqB,CAAC;EAAElB;AAAM,CAAC,KAAKA,KAAK,CAACQ,YAAY,CAACO,EAAE;AACzD;AACA;AACA;AACA,qBAAqB,CAAC;EAAEf;AAAM,CAAC,KAAKA,KAAK,CAACQ,YAAY,CAACU,EAAE;AACzD;AACA;AACA;AACA,8BAA8B,CAAC;EAAElB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACkB,MAAM;AAChE;AACA;AACA;AACA,mCAAmC,CAAC;EAAEnB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACkB,MAAM;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,CAAC;EAAEnB;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACM,MAAM;AAC1D,4BAA4B,CAAC;EAAEP;AAAM,CAAC,KAAKA,KAAK,CAACC,MAAM,CAACU,OAAO;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,CAAC;EAAEX;AAAM,CAAC,KAAKA,KAAK,CAACoB,WAAW,CAACH,EAAE;AAC1D;AACA,mBAAmB,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACc,OAAO,CAACG,EAAE;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,CAAC;EAAEjB;AAAM,CAAC,KAAKA,KAAK,CAACoB,WAAW,CAACH,EAAE;AAC1D;AACA;AACA;AACA;AACA,CAAC;AAED,eAAelB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}