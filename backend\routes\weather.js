const express = require('express');
const axios = require('axios');
const { param, query, validationResult } = require('express-validator');
const { optionalAuth } = require('../middleware/auth');

const router = express.Router();

const OPENWEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5';
const OPENWEATHER_GEO_URL = 'https://api.openweathermap.org/geo/1.0';

// Helper function to get weather data
const getWeatherData = async (url) => {
  try {
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Weather API Error: ${error.response.data.message || 'Unknown error'}`);
    }
    throw new Error('Failed to fetch weather data');
  }
};

// Helper function to format weather data
const formatWeatherData = (data, unit = 'metric') => {
  return {
    location: {
      name: data.name,
      country: data.sys.country,
      coordinates: {
        lat: data.coord.lat,
        lon: data.coord.lon
      },
      timezone: data.timezone,
      sunrise: new Date(data.sys.sunrise * 1000),
      sunset: new Date(data.sys.sunset * 1000)
    },
    current: {
      temperature: Math.round(data.main.temp),
      feelsLike: Math.round(data.main.feels_like),
      humidity: data.main.humidity,
      pressure: data.main.pressure,
      visibility: data.visibility / 1000, // Convert to km
      uvIndex: data.uvi || null,
      description: data.weather[0].description,
      main: data.weather[0].main,
      icon: data.weather[0].icon,
      windSpeed: data.wind.speed,
      windDirection: data.wind.deg,
      cloudiness: data.clouds.all
    },
    units: {
      temperature: unit === 'metric' ? '°C' : '°F',
      windSpeed: unit === 'metric' ? 'm/s' : 'mph',
      pressure: 'hPa',
      visibility: 'km'
    },
    timestamp: new Date()
  };
};

// Helper function to format forecast data
const formatForecastData = (data, unit = 'metric') => {
  const dailyForecasts = {};
  
  data.list.forEach(item => {
    const date = new Date(item.dt * 1000).toDateString();
    
    if (!dailyForecasts[date]) {
      dailyForecasts[date] = {
        date: new Date(item.dt * 1000),
        temperatures: [],
        conditions: [],
        humidity: [],
        windSpeed: [],
        main: item.weather[0].main,
        description: item.weather[0].description,
        icon: item.weather[0].icon
      };
    }
    
    dailyForecasts[date].temperatures.push(item.main.temp);
    dailyForecasts[date].humidity.push(item.main.humidity);
    dailyForecasts[date].windSpeed.push(item.wind.speed);
  });

  const forecast = Object.values(dailyForecasts).slice(0, 5).map(day => ({
    date: day.date,
    temperature: {
      min: Math.round(Math.min(...day.temperatures)),
      max: Math.round(Math.max(...day.temperatures))
    },
    humidity: Math.round(day.humidity.reduce((a, b) => a + b) / day.humidity.length),
    windSpeed: Math.round(day.windSpeed.reduce((a, b) => a + b) / day.windSpeed.length),
    main: day.main,
    description: day.description,
    icon: day.icon
  }));

  return {
    location: {
      name: data.city.name,
      country: data.city.country,
      coordinates: {
        lat: data.city.coord.lat,
        lon: data.city.coord.lon
      },
      timezone: data.city.timezone,
      sunrise: new Date(data.city.sunrise * 1000),
      sunset: new Date(data.city.sunset * 1000)
    },
    forecast,
    units: {
      temperature: unit === 'metric' ? '°C' : '°F',
      windSpeed: unit === 'metric' ? 'm/s' : 'mph'
    },
    timestamp: new Date()
  };
};

// @route   GET /api/weather/current/:city
// @desc    Get current weather for a city
// @access  Public
router.get('/current/:city', [
  param('city').trim().isLength({ min: 1 }).withMessage('City name is required'),
  query('units').optional().isIn(['metric', 'imperial']).withMessage('Units must be metric or imperial')
], optionalAuth, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { city } = req.params;
    const units = req.query.units || 'metric';
    const apiKey = process.env.OPENWEATHER_API_KEY;

    if (!apiKey) {
      return res.status(500).json({
        message: 'Weather API key not configured'
      });
    }

    const url = `${OPENWEATHER_BASE_URL}/weather?q=${encodeURIComponent(city)}&appid=${apiKey}&units=${units}`;
    const weatherData = await getWeatherData(url);
    
    const formattedData = formatWeatherData(weatherData, units);

    res.json({
      success: true,
      data: formattedData
    });

  } catch (error) {
    console.error('Current weather error:', error.message);
    res.status(500).json({
      message: 'Failed to fetch current weather',
      error: error.message
    });
  }
});

// @route   GET /api/weather/forecast/:city
// @desc    Get 5-day weather forecast for a city
// @access  Public
router.get('/forecast/:city', [
  param('city').trim().isLength({ min: 1 }).withMessage('City name is required'),
  query('units').optional().isIn(['metric', 'imperial']).withMessage('Units must be metric or imperial')
], optionalAuth, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { city } = req.params;
    const units = req.query.units || 'metric';
    const apiKey = process.env.OPENWEATHER_API_KEY;

    if (!apiKey) {
      return res.status(500).json({
        message: 'Weather API key not configured'
      });
    }

    const url = `${OPENWEATHER_BASE_URL}/forecast?q=${encodeURIComponent(city)}&appid=${apiKey}&units=${units}`;
    const forecastData = await getWeatherData(url);
    
    const formattedData = formatForecastData(forecastData, units);

    res.json({
      success: true,
      data: formattedData
    });

  } catch (error) {
    console.error('Forecast error:', error.message);
    res.status(500).json({
      message: 'Failed to fetch weather forecast',
      error: error.message
    });
  }
});

// @route   GET /api/weather/search/:query
// @desc    Search for cities (autocomplete)
// @access  Public
router.get('/search/:query', [
  param('query').trim().isLength({ min: 2 }).withMessage('Search query must be at least 2 characters'),
  query('limit').optional().isInt({ min: 1, max: 10 }).withMessage('Limit must be between 1 and 10')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { query } = req.params;
    const limit = req.query.limit || 5;
    const apiKey = process.env.OPENWEATHER_API_KEY;

    if (!apiKey) {
      return res.status(500).json({
        message: 'Weather API key not configured'
      });
    }

    const url = `${OPENWEATHER_GEO_URL}/direct?q=${encodeURIComponent(query)}&limit=${limit}&appid=${apiKey}`;
    const response = await axios.get(url);
    
    const cities = response.data.map(city => ({
      name: city.name,
      country: city.country,
      state: city.state || null,
      coordinates: {
        lat: city.lat,
        lon: city.lon
      },
      displayName: `${city.name}, ${city.state ? city.state + ', ' : ''}${city.country}`
    }));

    res.json({
      success: true,
      data: cities
    });

  } catch (error) {
    console.error('City search error:', error.message);
    res.status(500).json({
      message: 'Failed to search cities',
      error: error.message
    });
  }
});

module.exports = router;
